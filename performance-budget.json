{"budget": [{"type": "bundle", "name": "main", "baseline": "300kb", "maximum": "500kb", "warning": "400kb"}, {"type": "bundle", "name": "vendor", "baseline": "200kb", "maximum": "300kb", "warning": "250kb"}, {"type": "bundle", "name": "styles", "baseline": "50kb", "maximum": "100kb", "warning": "75kb"}, {"type": "asset", "name": "images", "baseline": "100kb", "maximum": "200kb", "warning": "150kb"}], "metrics": {"fcp": {"baseline": 1500, "maximum": 2000, "warning": 1800, "unit": "ms", "description": "First Contentful Paint"}, "lcp": {"baseline": 2000, "maximum": 2500, "warning": 2300, "unit": "ms", "description": "Largest Contentful Paint"}, "fid": {"baseline": 50, "maximum": 100, "warning": 75, "unit": "ms", "description": "First Input Delay"}, "cls": {"baseline": 0.05, "maximum": 0.1, "warning": 0.08, "unit": "score", "description": "Cumulative Layout Shift"}, "ttfb": {"baseline": 200, "maximum": 600, "warning": 400, "unit": "ms", "description": "Time to First Byte"}, "tti": {"baseline": 3000, "maximum": 5000, "warning": 4000, "unit": "ms", "description": "Time to Interactive"}}, "thresholds": {"lighthouse": {"performance": 90, "accessibility": 95, "best-practices": 90, "seo": 95, "pwa": 85}, "pagespeed": {"mobile": 85, "desktop": 90}}, "monitoring": {"enabled": true, "alerts": {"budget_exceeded": true, "performance_regression": true, "core_web_vitals": true}, "reporting": {"frequency": "daily", "recipients": ["<EMAIL>"], "dashboard": true}}, "optimization_targets": {"bundle_splitting": {"vendor_chunk": true, "async_chunks": true, "common_chunk": true}, "compression": {"gzip": true, "brotli": true}, "caching": {"static_assets": "1y", "dynamic_content": "1h", "api_responses": "5m"}, "images": {"lazy_loading": true, "responsive": true, "modern_formats": ["webp", "avif"], "compression": 80}, "fonts": {"preload": true, "display": "swap", "subset": true}}, "tools": {"lighthouse": {"enabled": true, "config": "lighthouse.config.js"}, "webpack_bundle_analyzer": {"enabled": true, "open": false}, "source_map_explorer": {"enabled": true}}}