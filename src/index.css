@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 183 100% 28%;
    --primary-foreground: 0 0% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 183 100% 28%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 183 100% 28%;

    --radius: 0.75rem;
    
    /* Portfolio specific tokens */
    --teal-dark: 184 91% 17%;
    --teal-light: 183 100% 28%;
    --gradient-primary: linear-gradient(135deg, hsl(var(--teal-dark)), hsl(var(--teal-light)));
    --gradient-hero: linear-gradient(135deg, hsl(var(--teal-dark)) 0%, hsl(var(--teal-light)) 50%, hsl(0 0% 100%) 100%);
    --shadow-portfolio: 0 10px 30px -10px hsl(var(--teal-light) / 0.3);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  .shadow-portfolio {
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }
}

/* Performance Mode Styles */
.performance-mode * {
  animation-duration: 0.1s !important;
  animation-delay: 0s !important;
  transition-duration: 0.1s !important;
  transition-delay: 0s !important;
}

.performance-mode .animate-pulse,
.performance-mode .animate-spin,
.performance-mode .animate-bounce {
  animation: none !important;
}

.performance-mode [data-motion="true"] {
  transform: none !important;
}

/* Reduced Motion Styles */
.reduced-motion * {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Connection-based Optimizations */
.connection-slow-2g img:not([loading="eager"]) {
  loading: lazy;
}

.connection-slow-2g video {
  preload: none;
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-gradient-hero {
    background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
  }

  .text-muted-foreground {
    color: hsl(var(--foreground));
  }
}

/* Mobile Optimizations */
@media (max-width: 768px) {
  /* Touch-friendly sizing */
  button, .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* Improved text readability */
  body {
    font-size: 16px;
    line-height: 1.6;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }

  /* Smooth scrolling for mobile */
  html {
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
  }

  /* Hide scrollbars on mobile */
  ::-webkit-scrollbar {
    display: none;
  }

  /* Optimize animations for mobile */
  * {
    animation-duration: 0.3s !important;
    transition-duration: 0.3s !important;
  }

  /* Better spacing for mobile */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile-specific utilities */
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none;
  }

  .desktop-only {
    display: block;
  }
}

/* Touch device optimizations */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover-effect:hover {
    transform: none;
  }

  /* Larger touch targets */
  button, a, .interactive {
    padding: 0.75rem;
  }

  /* Prevent text selection on interactive elements */
  .no-select {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Crisp images on retina displays */
  img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Landscape orientation on mobile */
@media (max-width: 768px) and (orientation: landscape) {
  /* Adjust header height for landscape */
  .mobile-header {
    height: 60px;
  }

  /* Optimize content for landscape */
  .landscape-optimize {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  * {
    animation: none !important;
    transition: none !important;
  }

  body {
    background: white !important;
    color: black !important;
  }
}