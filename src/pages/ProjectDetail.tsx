import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ExternalLink, Github, Calendar, Users, TrendingUp } from 'lucide-react';
import PageTransition from '@/components/PageTransition';
import SEOHead, { seoConfigs } from '@/components/SEOHead';
import { useAnalytics } from '@/hooks/use-analytics';

interface Project {
  id: string;
  title: string;
  description: string;
  problem: string;
  solution: string;
  outcome: string;
  tech: string[];
  image?: string;
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
  duration?: string;
  team?: string;
  metrics?: string[];
  challenges?: string[];
  learnings?: string[];
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent - Real Estate & Local Services Platform',
    description: 'Revolutionary rental platform that transformed Port Harcourt\'s rental market with verified agents, escrow-based booking, and integrated home services.',
    problem: 'Rental search in Port Harcourt lacked trust, speed, and verified agents. Manual processes led to fraud, delays, and poor user experience.',
    solution: 'Built full app architecture with React Native frontend, Node.js backend, Firebase integration, and Paystack payments. Included tenant listings, agent verification tools, service layers, and pioneered escrow-based rental booking via WhatsApp.',
    outcome: 'Achieved 40+ completed rentals in pilot phase with ₦200k+ GMV. Verified home services module (repairs, cleaning) added new revenue stream. Pioneered escrow-based rental booking experience.',
    tech: ['React Native', 'Node.js', 'Firebase', 'Paystack', 'TailwindCSS', 'WhatsApp API'],
    duration: '8 months',
    team: 'Full-stack development lead',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true,
    metrics: ['40+ completed rentals', '₦200k+ GMV', 'Escrow-based booking', 'Home services revenue stream'],
    challenges: [
      'Building trust in a market with high fraud rates',
      'Integrating WhatsApp for seamless booking experience',
      'Creating escrow system for secure transactions',
      'Scaling verified agent network'
    ],
    learnings: [
      'WhatsApp integration can dramatically improve user adoption in Nigerian markets',
      'Clear communication channels reduce support tickets by 60%',
      'Automated verification systems improve listing quality'
    ]
  },
  {
    id: 'medequip',
    title: 'MedEquip - Health Logistics Dashboard (Draycore)',
    description: 'Dynamic healthcare dashboard that revolutionized medical equipment tracking and reduced asset management time by 60%.',
    problem: 'Manual tracking of medical equipment across hospitals led to significant downtime, asset loss, and poor inter-hospital communication.',
    solution: 'Built comprehensive dynamic dashboard with Next.js and React, featuring sourcing modules, real-time inventory tracking, performance analytics, and modular component system for scalability.',
    outcome: 'Reduced asset tracking time by 60%, improved inter-hospital communication via real-time metrics, and created modular component system reusable across 5+ other projects.',
    tech: ['Next.js', 'React', 'Tailwind', 'Node.js', 'Firebase', 'Chart.js'],
    duration: '6 months',
    team: 'Lead developer at Draycore',
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true,
    metrics: ['60% faster asset tracking', 'Real-time inter-hospital metrics', '5+ project component reuse'],
    challenges: [
      'Creating real-time synchronization across multiple hospitals',
      'Building modular components for future project scalability',
      'Integrating complex sourcing and inventory workflows',
      'Ensuring data accuracy in critical healthcare environments'
    ],
    learnings: [
      'Modular component architecture significantly accelerates future development',
      'Real-time data visualization transforms decision-making in healthcare',
      'Firebase provides excellent real-time capabilities for healthcare dashboards'
    ]
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite - Patient Data Visualization Tool',
    description: 'Interactive healthcare dashboard that revolutionized patient data interpretation with advanced D3.js visualizations.',
    problem: 'Doctors and researchers struggled with interpreting complex patient data visually, leading to slower diagnosis and research inefficiencies.',
    solution: 'Built interactive dashboards with React, Redux, and D3.js featuring visual graph layers for diagnostic clarity. Integrated TikZ/PGF for advanced mathematical visualizations and TypeScript for type safety.',
    outcome: '35% faster data interpretation for lab results. Successfully deployed across 3+ hospitals and 1 research center, transforming how medical professionals analyze patient data.',
    tech: ['React', 'Redux', 'D3.js', 'TikZ/PGF', 'TypeScript', 'Node.js'],
    duration: '8 months',
    team: 'Lead frontend developer',
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true,
    metrics: ['35% faster data interpretation', '3+ hospitals deployed', '1 research center', 'Advanced D3.js visualizations'],
    challenges: [
      'Creating intuitive data visualizations for complex medical data',
      'Integrating TikZ/PGF for mathematical graph rendering',
      'Ensuring visualizations work across different screen sizes',
      'Maintaining performance with large datasets'
    ],
    learnings: [
      'D3.js combined with React requires careful state management',
      'Medical professionals need immediate visual feedback for data interpretation',
      'TypeScript significantly improves code reliability in complex visualization projects'
    ]
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet - Web3 Platform',
    description: 'Secure cryptocurrency wallet with clean UX, smart contract integration, and rapid delivery despite market turbulence.',
    problem: 'Clients needed a secure crypto wallet with clean UX in an unstable market, requiring rapid development and deployment.',
    solution: 'Built core UI/UX with React, connected smart contracts using Solidity and Ethers.js, integrated Metamask for seamless Web3 interactions, and managed tight delivery timeline.',
    outcome: 'Enabled secure onboarding and token swaps. Project delivered feature-complete under 4 weeks despite market turbulence, demonstrating exceptional project management and technical execution.',
    tech: ['Solidity', 'React', 'Metamask', 'Smart Contracts', 'Ethers.js', 'Web3.js'],
    duration: '4 weeks',
    team: 'Lead developer and project manager',
    githubUrl: 'https://github.com/rainernsa/krypto-wallet',
    featured: false,
    metrics: ['4-week delivery', 'Secure onboarding', 'Token swap functionality', 'Market turbulence resilience'],
    challenges: [
      'Delivering feature-complete product in just 4 weeks',
      'Navigating volatile cryptocurrency market conditions',
      'Ensuring security while maintaining development speed',
      'Integrating complex smart contract functionality'
    ],
    learnings: [
      'Rapid prototyping and MVP approach crucial in volatile markets',
      'Smart contract integration requires careful security considerations',
      'Clean UX design significantly improves Web3 adoption',
      'Project management skills are as important as technical skills'
    ]
  }
];

const ProjectDetail = () => {
  const { id } = useParams<{ id: string }>();
  const project = projects.find(p => p.id === id);
  const { trackProjectView } = useAnalytics();

  // Track project view
  if (project) {
    trackProjectView(project.id, project.title);
  }

  if (!project) {
    return (
      <PageTransition>
        <SEOHead {...seoConfigs.notFound} />
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
            <Link to="/">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition className="min-h-screen bg-background pt-20">
      <SEOHead {...seoConfigs.project(project.title, project.description, project.id)} />
      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-br from-primary/5 to-secondary/5">
        <div className="container mx-auto px-4">
          <Link to="/" className="inline-flex items-center text-muted-foreground hover:text-primary mb-8 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Portfolio
          </Link>
          
          <div className="max-w-4xl">
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6">
              {project.title}
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              {project.description}
            </p>
            
            <div className="flex flex-wrap gap-4 mb-8">
              {project.liveUrl && (
                <Button size="lg" asChild>
                  <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Live Demo
                  </a>
                </Button>
              )}
              {project.githubUrl && (
                <Button variant="outline" size="lg" asChild>
                  <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                    <Github className="mr-2 h-4 w-4" />
                    View Code
                  </a>
                </Button>
              )}
            </div>
            
            <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
              {project.duration && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{project.duration}</span>
                </div>
              )}
              {project.team && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>{project.team}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-12">
                {/* Problem */}
                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold text-primary mb-4">The Challenge</h2>
                    <p className="text-muted-foreground leading-relaxed">{project.problem}</p>
                  </CardContent>
                </Card>

                {/* Solution */}
                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold text-primary mb-4">The Solution</h2>
                    <p className="text-muted-foreground leading-relaxed">{project.solution}</p>
                  </CardContent>
                </Card>

                {/* Challenges */}
                {project.challenges && (
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold text-primary mb-4">Key Challenges</h2>
                      <ul className="space-y-3">
                        {project.challenges.map((challenge, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-muted-foreground">{challenge}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* Learnings */}
                {project.learnings && (
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold text-primary mb-4">Key Learnings</h2>
                      <ul className="space-y-3">
                        {project.learnings.map((learning, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-secondary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-muted-foreground">{learning}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-8">
                {/* Metrics */}
                {project.metrics && (
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <TrendingUp className="h-5 w-5 text-primary" />
                        <h3 className="font-semibold">Key Results</h3>
                      </div>
                      <div className="space-y-3">
                        {project.metrics.map((metric, index) => (
                          <div key={index} className="p-3 bg-primary/5 rounded-lg">
                            <span className="font-medium text-sm">{metric}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Tech Stack */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Technology Stack</h3>
                    <div className="flex flex-wrap gap-2">
                      {project.tech.map((tech) => (
                        <Badge key={tech} variant="secondary">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Outcome */}
                <Card className="bg-primary text-primary-foreground">
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Final Outcome</h3>
                    <p className="text-primary-foreground/90 text-sm leading-relaxed">
                      {project.outcome}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PageTransition>
  );
};

export default ProjectDetail;