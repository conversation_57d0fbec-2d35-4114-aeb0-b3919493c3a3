import { <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, ExternalLink, Github, Calendar, Users, TrendingUp } from 'lucide-react';
import PageTransition from '@/components/PageTransition';

interface Project {
  id: string;
  title: string;
  description: string;
  problem: string;
  solution: string;
  outcome: string;
  tech: string[];
  image?: string;
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
  duration?: string;
  team?: string;
  metrics?: string[];
  challenges?: string[];
  learnings?: string[];
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent',
    description: 'A comprehensive rental platform for Port Harcourt that transformed the local rental market.',
    problem: 'Port Harcourt residents struggled with finding reliable rental properties due to fragmented listing systems and lack of trust between landlords and tenants.',
    solution: 'Built a comprehensive rental management platform with advanced search filters, verified listings, secure booking system, integrated payment processing, and user reviews system.',
    outcome: 'Successfully increased user base by 150% within 6 months, facilitated over ₦50M in rental transactions, and reduced average property search time by 70%.',
    tech: ['React', 'Node.js', 'PostgreSQL', 'Stripe', 'Redis', 'AWS S3'],
    duration: '6 months',
    team: '4 developers',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true,
    metrics: ['150% user growth', '₦50M+ transactions', '70% faster searches'],
    challenges: [
      'Building trust between strangers in rental transactions',
      'Handling complex payment flows with multiple stakeholders',
      'Scaling the platform to handle growing user base'
    ],
    learnings: [
      'User trust is paramount in marketplace applications',
      'Clear communication channels reduce support tickets by 60%',
      'Automated verification systems improve listing quality'
    ]
  },
  {
    id: 'medequip',
    title: 'MedEquip',
    description: 'Hospital equipment management system that revolutionized medical equipment tracking across Nigeria.',
    problem: 'Hospitals across Nigeria struggled with equipment tracking, maintenance scheduling, and cost optimization due to manual processes and poor visibility.',
    solution: 'Developed a comprehensive equipment management system with real-time tracking, predictive maintenance algorithms, inventory optimization, and detailed analytics dashboard.',
    outcome: 'Successfully reduced equipment costs by 50%, improved equipment uptime to 98%, and streamlined maintenance workflows across 25+ hospitals.',
    tech: ['React', 'Express', 'MongoDB', 'Chart.js', 'Socket.io', 'Docker'],
    duration: '8 months',
    team: '6 developers',
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true,
    metrics: ['50% cost reduction', '98% equipment uptime', '25+ hospitals'],
    challenges: [
      'Integrating with legacy hospital systems',
      'Training staff on new digital workflows',
      'Ensuring system reliability in critical healthcare environments'
    ],
    learnings: [
      'Healthcare systems require exceptional reliability and uptime',
      'User training and change management are crucial for adoption',
      'Data visualization helps stakeholders make informed decisions'
    ]
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite',
    description: 'Advanced healthcare dashboard that transformed medical data reporting and analysis.',
    problem: 'Healthcare facilities needed better insights from patient data but struggled with manual reporting processes that took weeks to complete.',
    solution: 'Built a comprehensive health management platform with real-time analytics, automated reporting, patient tracking, and interactive data visualizations using advanced charts and graphs.',
    outcome: 'Reduced reporting time by 35% initially, then optimized further to achieve 58% reduction in reporting delays, while improving data accuracy by 40%.',
    tech: ['Next.js', 'TypeScript', 'PostgreSQL', 'D3.js', 'Prisma', 'Vercel'],
    duration: '10 months',
    team: '5 developers',
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true,
    metrics: ['58% faster reporting', '40% improved accuracy', '15+ clinics'],
    challenges: [
      'Handling sensitive patient data with HIPAA compliance',
      'Creating intuitive interfaces for non-technical medical staff',
      'Processing large datasets efficiently for real-time insights'
    ],
    learnings: [
      'Data privacy and security cannot be compromised in healthcare',
      'Simple, intuitive UX is critical for busy medical professionals',
      'Real-time data processing requires careful architecture planning'
    ]
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet',
    description: 'Secure cryptocurrency wallet with multi-chain support and advanced security features.',
    problem: 'Users needed a secure, user-friendly wallet that could handle multiple cryptocurrencies while maintaining the highest security standards.',
    solution: 'Developed a mobile-first wallet with biometric authentication, multi-signature support, cross-chain transactions, and hardware wallet integration.',
    outcome: 'Successfully processed over $2M in secure transactions with zero security incidents, gained 10,000+ active users.',
    tech: ['React Native', 'Web3.js', 'Solidity', 'Node.js', 'Expo', 'Firebase'],
    duration: '12 months',
    team: '3 developers',
    liveUrl: 'https://kryptowallet.app',
    githubUrl: 'https://github.com/rainernsa/krypto-wallet',
    featured: false,
    metrics: ['$2M+ processed', '10K+ users', '0 security incidents'],
    challenges: [
      'Implementing robust security measures without compromising UX',
      'Supporting multiple blockchain networks with different protocols',
      'Educating users about cryptocurrency security best practices'
    ],
    learnings: [
      'Security and usability must be balanced carefully in fintech',
      'User education is crucial for cryptocurrency adoption',
      'Thorough testing is essential when handling financial assets'
    ]
  }
];

const ProjectDetail = () => {
  const { id } = useParams<{ id: string }>();
  const project = projects.find(p => p.id === id);

  if (!project) {
    return (
      <PageTransition>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Project Not Found</h1>
            <Link to="/">
              <Button>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </PageTransition>
    );
  }

  return (
    <PageTransition className="min-h-screen bg-background pt-20">
      {/* Hero Section */}
      <section className="py-12 bg-gradient-to-br from-primary/5 to-secondary/5">
        <div className="container mx-auto px-4">
          <Link to="/" className="inline-flex items-center text-muted-foreground hover:text-primary mb-8 transition-colors">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Portfolio
          </Link>
          
          <div className="max-w-4xl">
            <h1 className="text-4xl lg:text-6xl font-bold text-foreground mb-6">
              {project.title}
            </h1>
            <p className="text-xl text-muted-foreground mb-8 leading-relaxed">
              {project.description}
            </p>
            
            <div className="flex flex-wrap gap-4 mb-8">
              {project.liveUrl && (
                <Button size="lg" asChild>
                  <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    View Live Demo
                  </a>
                </Button>
              )}
              {project.githubUrl && (
                <Button variant="outline" size="lg" asChild>
                  <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                    <Github className="mr-2 h-4 w-4" />
                    View Code
                  </a>
                </Button>
              )}
            </div>
            
            <div className="flex flex-wrap gap-6 text-sm text-muted-foreground">
              {project.duration && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>{project.duration}</span>
                </div>
              )}
              {project.team && (
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>{project.team}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid lg:grid-cols-3 gap-12">
              {/* Main Content */}
              <div className="lg:col-span-2 space-y-12">
                {/* Problem */}
                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold text-primary mb-4">The Challenge</h2>
                    <p className="text-muted-foreground leading-relaxed">{project.problem}</p>
                  </CardContent>
                </Card>

                {/* Solution */}
                <Card>
                  <CardContent className="p-8">
                    <h2 className="text-2xl font-bold text-primary mb-4">The Solution</h2>
                    <p className="text-muted-foreground leading-relaxed">{project.solution}</p>
                  </CardContent>
                </Card>

                {/* Challenges */}
                {project.challenges && (
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold text-primary mb-4">Key Challenges</h2>
                      <ul className="space-y-3">
                        {project.challenges.map((challenge, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-muted-foreground">{challenge}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}

                {/* Learnings */}
                {project.learnings && (
                  <Card>
                    <CardContent className="p-8">
                      <h2 className="text-2xl font-bold text-primary mb-4">Key Learnings</h2>
                      <ul className="space-y-3">
                        {project.learnings.map((learning, index) => (
                          <li key={index} className="flex items-start gap-3">
                            <div className="w-2 h-2 bg-secondary rounded-full mt-2 flex-shrink-0" />
                            <span className="text-muted-foreground">{learning}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Sidebar */}
              <div className="space-y-8">
                {/* Metrics */}
                {project.metrics && (
                  <Card>
                    <CardContent className="p-6">
                      <div className="flex items-center gap-2 mb-4">
                        <TrendingUp className="h-5 w-5 text-primary" />
                        <h3 className="font-semibold">Key Results</h3>
                      </div>
                      <div className="space-y-3">
                        {project.metrics.map((metric, index) => (
                          <div key={index} className="p-3 bg-primary/5 rounded-lg">
                            <span className="font-medium text-sm">{metric}</span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Tech Stack */}
                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Technology Stack</h3>
                    <div className="flex flex-wrap gap-2">
                      {project.tech.map((tech) => (
                        <Badge key={tech} variant="secondary">
                          {tech}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Outcome */}
                <Card className="bg-primary text-primary-foreground">
                  <CardContent className="p-6">
                    <h3 className="font-semibold mb-4">Final Outcome</h3>
                    <p className="text-primary-foreground/90 text-sm leading-relaxed">
                      {project.outcome}
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>
    </PageTransition>
  );
};

export default ProjectDetail;