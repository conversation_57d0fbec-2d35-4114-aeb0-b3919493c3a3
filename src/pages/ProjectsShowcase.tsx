import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { EnterpriseCard, EnterpriseCardContent, EnterpriseCardHeader, EnterpriseCardTitle, EnterpriseBadge } from '@/components/ui/enterprise-card';
import { EnterpriseButton } from '@/components/ui/enterprise-button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  ExternalLink, 
  Github, 
  Calendar, 
  TrendingUp, 
  Users, 
  DollarSign,
  Award,
  Building,
  Zap,
  Globe,
  ArrowRight,
  Star
} from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';
import LazyImage from '@/components/ui/lazy-image';

interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  category: string;
  industry: string;
  tech: string[];
  image: string;
  liveUrl?: string;
  githubUrl?: string;
  featured: boolean;
  status: 'completed' | 'in-progress' | 'maintenance';
  year: number;
  duration: string;
  teamSize: number;
  budget: string;
  impact: {
    users?: string;
    revenue?: string;
    performance?: string;
    satisfaction?: string;
  };
  awards?: string[];
  client: {
    name: string;
    type: 'Enterprise' | 'Startup' | 'SMB' | 'Personal';
    logo?: string;
  };
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent - Real Estate & Local Services Platform',
    description: 'Revolutionary rental platform with verified agents, escrow booking, and home services',
    longDescription: 'Full-stack real estate platform built with React Native and Node.js that transformed Port Harcourt\'s rental market. Features verified agent network, escrow-based booking via WhatsApp, and integrated home services (repairs, cleaning) creating new revenue streams. Achieved 40+ completed rentals with ₦200k+ GMV in pilot phase.',
    category: 'Mobile Application',
    industry: 'Real Estate & Services',
    tech: ['React Native', 'Node.js', 'Firebase', 'Paystack', 'TailwindCSS', 'WhatsApp API'],
    image: '/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true,
    status: 'completed',
    year: 2024,
    duration: '8 months',
    teamSize: 1,
    budget: 'Self-funded',
    impact: {
      users: '40+ rentals',
      revenue: '₦200K+ GMV',
      performance: '99%',
      satisfaction: '4.9/5'
    },
    awards: ['Employee of the Year - Woldreamz Inc (2021)', 'The Code Whisperer Award'],
    client: {
      name: 'Independent Project',
      type: 'Startup'
    }
  },
  {
    id: 'medequip',
    title: 'MedEquip - Health Logistics Dashboard (Draycore)',
    description: 'Dynamic healthcare dashboard reducing asset tracking time by 60%',
    longDescription: 'Comprehensive health logistics dashboard built for Draycore with Next.js and React. Features dynamic sourcing modules, real-time inventory tracking, and performance analytics. Reduced asset tracking time by 60% and improved inter-hospital communication. Created modular component system reused across 5+ other projects.',
    category: 'Healthcare Dashboard',
    industry: 'Healthcare Technology',
    tech: ['Next.js', 'React', 'Tailwind', 'Node.js', 'Firebase', 'Chart.js'],
    image: '/lovable-uploads/medequip-preview.png',
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true,
    status: 'completed',
    year: 2024,
    duration: '6 months',
    teamSize: 1,
    budget: 'Enterprise Contract',
    impact: {
      users: 'Multiple hospitals',
      revenue: '60% efficiency gain',
      performance: '99%',
      satisfaction: '4.8/5'
    },
    awards: ['Modular Architecture Excellence'],
    client: {
      name: 'Draycore',
      type: 'Enterprise'
    }
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite - Patient Data Visualization Tool',
    description: 'Interactive healthcare dashboard with advanced D3.js visualizations',
    longDescription: 'Advanced patient data visualization tool built with React, Redux, and D3.js. Features interactive dashboards and visual graph layers for diagnostic clarity, with TikZ/PGF integration for mathematical visualizations. Achieved 35% faster data interpretation for lab results and deployed across 3+ hospitals and 1 research center.',
    category: 'Data Visualization',
    industry: 'Healthcare Research',
    tech: ['React', 'Redux', 'D3.js', 'TikZ/PGF', 'TypeScript', 'Node.js'],
    image: '/lovable-uploads/infinity-health-preview.png',
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true,
    status: 'completed',
    year: 2023,
    duration: '8 months',
    teamSize: 1,
    budget: 'Research Grant',
    impact: {
      users: '3+ hospitals',
      revenue: '35% faster diagnosis',
      performance: '96%',
      satisfaction: '4.9/5'
    },
    client: {
      name: 'Healthcare Research Consortium',
      type: 'Enterprise'
    }
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet - Web3 Platform',
    description: 'Secure cryptocurrency wallet delivered in 4 weeks despite market turbulence',
    longDescription: 'Rapid-development Web3 platform built with React and Solidity. Features secure onboarding, token swap functionality, and Metamask integration. Delivered feature-complete in just 4 weeks despite volatile cryptocurrency market conditions, demonstrating exceptional project management and technical execution under pressure.',
    category: 'Web3 Application',
    industry: 'Cryptocurrency',
    tech: ['Solidity', 'React', 'Metamask', 'Smart Contracts', 'Ethers.js', 'Web3.js'],
    image: '/lovable-uploads/krypto-wallet-preview.png',
    githubUrl: 'https://github.com/rainernsa/krypto-wallet',
    featured: false,
    status: 'completed',
    year: 2023,
    duration: '4 weeks',
    teamSize: 1,
    budget: 'Fixed Contract',
    impact: {
      users: 'Secure onboarding',
      revenue: 'Token swap enabled',
      performance: '98%',
      satisfaction: '4.7/5'
    },
    client: {
      name: 'Crypto Startup',
      type: 'Startup'
    }
  }
];

const ProjectsShowcase = () => {
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('year');
  const { trackEvent } = useAnalytics();

  const categories = ['all', ...Array.from(new Set(projects.map(p => p.category)))];
  const industries = ['all', ...Array.from(new Set(projects.map(p => p.industry)))];

  useEffect(() => {
    let filtered = projects;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.tech.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(project => project.category === categoryFilter);
    }

    // Industry filter
    if (industryFilter !== 'all') {
      filtered = filtered.filter(project => project.industry === industryFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'year':
          return b.year - a.year;
        case 'title':
          return a.title.localeCompare(b.title);
        case 'featured':
          return b.featured ? 1 : -1;
        default:
          return 0;
      }
    });

    setFilteredProjects(filtered);
  }, [searchTerm, categoryFilter, industryFilter, sortBy]);

  const handleProjectClick = (project: Project) => {
    trackEvent('project_showcase_click', {
      project_id: project.id,
      project_title: project.title,
      category: project.category
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'maintenance':
        return 'default';
      default:
        return 'default';
    }
  };

  const getClientTypeColor = (type: string) => {
    switch (type) {
      case 'Enterprise':
        return 'executive';
      case 'Startup':
        return 'premium';
      case 'SMB':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-950 dark:via-slate-900 dark:to-blue-950">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-8">
              <Building className="w-5 h-5" />
              <span className="font-semibold">Enterprise Portfolio</span>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-black mb-6 leading-tight">
              <span className="block">Project</span>
              <span className="block bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Showcase
              </span>
            </h1>
            
            <p className="text-xl lg:text-2xl text-white/90 leading-relaxed mb-8 max-w-3xl mx-auto">
              Explore my comprehensive portfolio of real-world solutions that have generated
              ₦200K+ GMV, achieved 60% efficiency gains, and served clients across 3 continents.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-black mb-2">4+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Major Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">₂00K+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">GMV Generated</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">15+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Engineers Mentored</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">3</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Continents Served</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-12 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Search projects, technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 text-base"
                />
              </div>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={industryFilter} onValueChange={setIndustryFilter}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map(industry => (
                    <SelectItem key={industry} value={industry}>
                      {industry === 'all' ? 'All Industries' : industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="year">Latest First</SelectItem>
                  <SelectItem value="title">Alphabetical</SelectItem>
                  <SelectItem value="featured">Featured First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <AnimatePresence>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  onClick={() => handleProjectClick(project)}
                >
                  <EnterpriseCard 
                    variant={project.featured ? "executive" : "elevated"} 
                    className="h-full cursor-pointer group"
                  >
                    <EnterpriseCardHeader>
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex flex-wrap gap-2">
                          {project.featured && (
                            <EnterpriseBadge variant="executive" size="sm">
                              <Star className="w-3 h-3 mr-1" />
                              Featured
                            </EnterpriseBadge>
                          )}
                          <EnterpriseBadge variant={getStatusColor(project.status)} size="sm">
                            {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                          </EnterpriseBadge>
                          <EnterpriseBadge variant={getClientTypeColor(project.client.type)} size="sm">
                            {project.client.type}
                          </EnterpriseBadge>
                        </div>
                        <div className="text-sm text-muted-foreground font-medium">
                          {project.year}
                        </div>
                      </div>

                      <div className="relative mb-6 overflow-hidden rounded-lg">
                        <LazyImage
                          src={project.image}
                          alt={`${project.title} preview`}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          containerClassName="w-full h-48"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>

                      <EnterpriseCardTitle className="mb-3 group-hover:text-primary transition-colors">
                        {project.title}
                      </EnterpriseCardTitle>
                      
                      <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                        {project.description}
                      </p>
                    </EnterpriseCardHeader>

                    <EnterpriseCardContent>
                      {/* Impact Metrics */}
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        {project.impact.users && (
                          <div className="text-center p-3 bg-muted/30 rounded-lg">
                            <Users className="w-4 h-4 mx-auto mb-1 text-primary" />
                            <div className="text-sm font-bold">{project.impact.users}</div>
                            <div className="text-xs text-muted-foreground">Users</div>
                          </div>
                        )}
                        {project.impact.revenue && (
                          <div className="text-center p-3 bg-muted/30 rounded-lg">
                            <DollarSign className="w-4 h-4 mx-auto mb-1 text-green-600" />
                            <div className="text-sm font-bold">{project.impact.revenue}</div>
                            <div className="text-xs text-muted-foreground">Revenue</div>
                          </div>
                        )}
                      </div>

                      {/* Tech Stack */}
                      <div className="flex flex-wrap gap-2 mb-6">
                        {project.tech.slice(0, 4).map((tech) => (
                          <Badge key={tech} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {project.tech.length > 4 && (
                          <Badge variant="secondary" className="text-xs">
                            +{project.tech.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <Link to={`/project/${project.id}`} className="flex-1">
                          <EnterpriseButton variant="default" size="sm" className="w-full">
                            View Details
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </EnterpriseButton>
                        </Link>
                        {project.liveUrl && (
                          <EnterpriseButton variant="outline" size="sm" asChild>
                            <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          </EnterpriseButton>
                        )}
                        {project.githubUrl && (
                          <EnterpriseButton variant="outline" size="sm" asChild>
                            <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                              <Github className="w-4 h-4" />
                            </a>
                          </EnterpriseButton>
                        )}
                      </div>
                    </EnterpriseCardContent>
                  </EnterpriseCard>
                </motion.div>
              ))}
            </div>
          </AnimatePresence>

          {filteredProjects.length === 0 && (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold text-foreground mb-2">No projects found</h3>
              <p className="text-muted-foreground">Try adjusting your search criteria</p>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 to-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl lg:text-5xl font-black mb-6">
              Ready to Build Something
              <span className="block bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Extraordinary?
              </span>
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Let's discuss how we can transform your vision into a market-leading digital solution.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <EnterpriseButton variant="premium" size="xl" asChild>
                <Link to="/contact">
                  Start Your Project
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </EnterpriseButton>
              <EnterpriseButton variant="outline" size="xl" asChild>
                <Link to="/">
                  <Globe className="w-5 h-5 mr-2" />
                  Back to Portfolio
                </Link>
              </EnterpriseButton>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProjectsShowcase;
