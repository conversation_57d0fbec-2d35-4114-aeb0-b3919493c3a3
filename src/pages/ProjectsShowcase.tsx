import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import { EnterpriseCard, EnterpriseCardContent, EnterpriseCardHeader, EnterpriseCardTitle, EnterpriseBadge } from '@/components/ui/enterprise-card';
import { EnterpriseButton } from '@/components/ui/enterprise-button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Filter, 
  ExternalLink, 
  Github, 
  Calendar, 
  TrendingUp, 
  Users, 
  DollarSign,
  Award,
  Building,
  Zap,
  Globe,
  ArrowRight,
  Star
} from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';
import LazyImage from '@/components/ui/lazy-image';

interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  category: string;
  industry: string;
  tech: string[];
  image: string;
  liveUrl?: string;
  githubUrl?: string;
  featured: boolean;
  status: 'completed' | 'in-progress' | 'maintenance';
  year: number;
  duration: string;
  teamSize: number;
  budget: string;
  impact: {
    users?: string;
    revenue?: string;
    performance?: string;
    satisfaction?: string;
  };
  awards?: string[];
  client: {
    name: string;
    type: 'Enterprise' | 'Startup' | 'SMB' | 'Personal';
    logo?: string;
  };
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent Platform',
    description: 'Revolutionary real estate platform transforming property rental in Port Harcourt',
    longDescription: 'A comprehensive real estate platform that revolutionized property rental in Port Harcourt with advanced search capabilities, virtual tours, and seamless booking system. Built with enterprise-grade architecture to handle high traffic and complex property data.',
    category: 'Web Application',
    industry: 'Real Estate',
    tech: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe', 'Redis'],
    image: '/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true,
    status: 'completed',
    year: 2024,
    duration: '8 months',
    teamSize: 3,
    budget: '$75K',
    impact: {
      users: '10K+',
      revenue: '$500K+',
      performance: '98%',
      satisfaction: '4.8/5'
    },
    awards: ['Best Real Estate Platform 2024'],
    client: {
      name: 'PHCity Holdings',
      type: 'Enterprise'
    }
  },
  {
    id: 'medequip',
    title: 'MedEquip Management System',
    description: 'Comprehensive medical equipment management system for healthcare facilities',
    longDescription: 'Enterprise-grade medical equipment management system that reduced operational costs by 50% and improved maintenance scheduling by 80%. Deployed across 15+ healthcare facilities with real-time inventory tracking and predictive maintenance.',
    category: 'Enterprise Software',
    industry: 'Healthcare',
    tech: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Vercel', 'Stripe'],
    image: '/lovable-uploads/medequip-preview.png',
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true,
    status: 'completed',
    year: 2024,
    duration: '6 months',
    teamSize: 4,
    budget: '$120K',
    impact: {
      users: '500+',
      revenue: '$250K+',
      performance: '95%',
      satisfaction: '4.9/5'
    },
    awards: ['Healthcare Innovation Award 2024'],
    client: {
      name: 'MedTech Solutions',
      type: 'Enterprise'
    }
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite',
    description: 'Advanced healthcare data management platform with powerful analytics',
    longDescription: 'Sophisticated healthcare data management platform featuring advanced analytics, reporting capabilities, and seamless integration with existing healthcare systems. Reduced reporting time by 58% and improved data accuracy by 40%.',
    category: 'Data Platform',
    industry: 'Healthcare',
    tech: ['Next.js', 'D3.js', 'PostgreSQL', 'Node.js', 'Chart.js', 'Docker'],
    image: '/lovable-uploads/infinity-health-preview.png',
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true,
    status: 'completed',
    year: 2023,
    duration: '10 months',
    teamSize: 5,
    budget: '$200K',
    impact: {
      users: '1K+',
      revenue: '$150K+',
      performance: '92%',
      satisfaction: '4.7/5'
    },
    client: {
      name: 'HealthTech Innovations',
      type: 'Startup'
    }
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet Interface',
    description: 'Modern cryptocurrency wallet interface with advanced security features',
    longDescription: 'Cutting-edge cryptocurrency wallet interface built with modern security practices, multi-currency support, and intuitive user experience. Features advanced encryption, biometric authentication, and real-time market data.',
    category: 'Fintech Application',
    industry: 'Financial Services',
    tech: ['React', 'Web3.js', 'Ethereum', 'TypeScript', 'Tailwind', 'MetaMask'],
    image: '/lovable-uploads/krypto-wallet-preview.png',
    githubUrl: 'https://github.com/rainernsa/krypto-wallet',
    featured: false,
    status: 'completed',
    year: 2023,
    duration: '4 months',
    teamSize: 2,
    budget: '$50K',
    impact: {
      users: '2K+',
      performance: '96%',
      satisfaction: '4.6/5'
    },
    client: {
      name: 'CryptoTech Ventures',
      type: 'Startup'
    }
  }
];

const ProjectsShowcase = () => {
  const [filteredProjects, setFilteredProjects] = useState(projects);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [industryFilter, setIndustryFilter] = useState('all');
  const [sortBy, setSortBy] = useState('year');
  const { trackEvent } = useAnalytics();

  const categories = ['all', ...Array.from(new Set(projects.map(p => p.category)))];
  const industries = ['all', ...Array.from(new Set(projects.map(p => p.industry)))];

  useEffect(() => {
    let filtered = projects;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.tech.some(tech => tech.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Category filter
    if (categoryFilter !== 'all') {
      filtered = filtered.filter(project => project.category === categoryFilter);
    }

    // Industry filter
    if (industryFilter !== 'all') {
      filtered = filtered.filter(project => project.industry === industryFilter);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'year':
          return b.year - a.year;
        case 'title':
          return a.title.localeCompare(b.title);
        case 'featured':
          return b.featured ? 1 : -1;
        default:
          return 0;
      }
    });

    setFilteredProjects(filtered);
  }, [searchTerm, categoryFilter, industryFilter, sortBy]);

  const handleProjectClick = (project: Project) => {
    trackEvent('project_showcase_click', {
      project_id: project.id,
      project_title: project.title,
      category: project.category
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in-progress':
        return 'warning';
      case 'maintenance':
        return 'default';
      default:
        return 'default';
    }
  };

  const getClientTypeColor = (type: string) => {
    switch (type) {
      case 'Enterprise':
        return 'executive';
      case 'Startup':
        return 'premium';
      case 'SMB':
        return 'default';
      default:
        return 'default';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50 dark:from-slate-950 dark:via-slate-900 dark:to-blue-950">
      {/* Hero Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 text-white">
        <div className="container mx-auto px-4">
          <motion.div
            className="text-center max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-sm rounded-full border border-white/20 mb-8">
              <Building className="w-5 h-5" />
              <span className="font-semibold">Enterprise Portfolio</span>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-black mb-6 leading-tight">
              <span className="block">Project</span>
              <span className="block bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Showcase
              </span>
            </h1>
            
            <p className="text-xl lg:text-2xl text-white/90 leading-relaxed mb-8 max-w-3xl mx-auto">
              Explore our comprehensive portfolio of enterprise-grade solutions that have generated 
              over $2M in revenue and served 25+ clients across multiple industries.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-12">
              <div className="text-center">
                <div className="text-3xl font-black mb-2">25+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Projects Delivered</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">$2M+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Revenue Generated</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">15+</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Industries Served</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-black mb-2">99.9%</div>
                <div className="text-sm text-white/70 uppercase tracking-wide">Success Rate</div>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-12 bg-white/50 dark:bg-slate-900/50 backdrop-blur-sm border-b border-slate-200 dark:border-slate-700">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-6 items-center justify-between">
            <div className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-5 h-5" />
                <Input
                  placeholder="Search projects, technologies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 h-12 text-base"
                />
              </div>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={industryFilter} onValueChange={setIndustryFilter}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Industry" />
                </SelectTrigger>
                <SelectContent>
                  {industries.map(industry => (
                    <SelectItem key={industry} value={industry}>
                      {industry === 'all' ? 'All Industries' : industry}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48 h-12">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="year">Latest First</SelectItem>
                  <SelectItem value="title">Alphabetical</SelectItem>
                  <SelectItem value="featured">Featured First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <AnimatePresence>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
              {filteredProjects.map((project, index) => (
                <motion.div
                  key={project.id}
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -30 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  onClick={() => handleProjectClick(project)}
                >
                  <EnterpriseCard 
                    variant={project.featured ? "executive" : "elevated"} 
                    className="h-full cursor-pointer group"
                  >
                    <EnterpriseCardHeader>
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex flex-wrap gap-2">
                          {project.featured && (
                            <EnterpriseBadge variant="executive" size="sm">
                              <Star className="w-3 h-3 mr-1" />
                              Featured
                            </EnterpriseBadge>
                          )}
                          <EnterpriseBadge variant={getStatusColor(project.status)} size="sm">
                            {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
                          </EnterpriseBadge>
                          <EnterpriseBadge variant={getClientTypeColor(project.client.type)} size="sm">
                            {project.client.type}
                          </EnterpriseBadge>
                        </div>
                        <div className="text-sm text-muted-foreground font-medium">
                          {project.year}
                        </div>
                      </div>

                      <div className="relative mb-6 overflow-hidden rounded-lg">
                        <LazyImage
                          src={project.image}
                          alt={`${project.title} preview`}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          containerClassName="w-full h-48"
                        />
                        <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />
                      </div>

                      <EnterpriseCardTitle className="mb-3 group-hover:text-primary transition-colors">
                        {project.title}
                      </EnterpriseCardTitle>
                      
                      <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                        {project.description}
                      </p>
                    </EnterpriseCardHeader>

                    <EnterpriseCardContent>
                      {/* Impact Metrics */}
                      <div className="grid grid-cols-2 gap-3 mb-6">
                        {project.impact.users && (
                          <div className="text-center p-3 bg-muted/30 rounded-lg">
                            <Users className="w-4 h-4 mx-auto mb-1 text-primary" />
                            <div className="text-sm font-bold">{project.impact.users}</div>
                            <div className="text-xs text-muted-foreground">Users</div>
                          </div>
                        )}
                        {project.impact.revenue && (
                          <div className="text-center p-3 bg-muted/30 rounded-lg">
                            <DollarSign className="w-4 h-4 mx-auto mb-1 text-green-600" />
                            <div className="text-sm font-bold">{project.impact.revenue}</div>
                            <div className="text-xs text-muted-foreground">Revenue</div>
                          </div>
                        )}
                      </div>

                      {/* Tech Stack */}
                      <div className="flex flex-wrap gap-2 mb-6">
                        {project.tech.slice(0, 4).map((tech) => (
                          <Badge key={tech} variant="secondary" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {project.tech.length > 4 && (
                          <Badge variant="secondary" className="text-xs">
                            +{project.tech.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-3">
                        <Link to={`/project/${project.id}`} className="flex-1">
                          <EnterpriseButton variant="default" size="sm" className="w-full">
                            View Details
                            <ArrowRight className="w-4 h-4 ml-2" />
                          </EnterpriseButton>
                        </Link>
                        {project.liveUrl && (
                          <EnterpriseButton variant="outline" size="sm" asChild>
                            <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="w-4 h-4" />
                            </a>
                          </EnterpriseButton>
                        )}
                        {project.githubUrl && (
                          <EnterpriseButton variant="outline" size="sm" asChild>
                            <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                              <Github className="w-4 h-4" />
                            </a>
                          </EnterpriseButton>
                        )}
                      </div>
                    </EnterpriseCardContent>
                  </EnterpriseCard>
                </motion.div>
              ))}
            </div>
          </AnimatePresence>

          {filteredProjects.length === 0 && (
            <motion.div
              className="text-center py-20"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
            >
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-bold text-foreground mb-2">No projects found</h3>
              <p className="text-muted-foreground">Try adjusting your search criteria</p>
            </motion.div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-slate-900 to-blue-900 text-white">
        <div className="container mx-auto px-4 text-center">
          <motion.div
            className="max-w-3xl mx-auto"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl lg:text-5xl font-black mb-6">
              Ready to Build Something
              <span className="block bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">
                Extraordinary?
              </span>
            </h2>
            <p className="text-xl text-white/90 mb-8 leading-relaxed">
              Let's discuss how we can transform your vision into a market-leading digital solution.
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <EnterpriseButton variant="premium" size="xl" asChild>
                <Link to="/contact">
                  Start Your Project
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </EnterpriseButton>
              <EnterpriseButton variant="outline" size="xl" asChild>
                <Link to="/">
                  <Globe className="w-5 h-5 mr-2" />
                  Back to Portfolio
                </Link>
              </EnterpriseButton>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default ProjectsShowcase;
