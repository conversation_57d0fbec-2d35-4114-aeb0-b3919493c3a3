import { useEffect, useCallback } from 'react';
import { useLocation } from 'react-router-dom';
import analytics from '@/lib/analytics';

export const useAnalytics = () => {
  const location = useLocation();

  // Initialize analytics on mount
  useEffect(() => {
    analytics.init();
    analytics.trackPerformance();
    analytics.trackEngagement();
  }, []);

  // Track page views on route changes
  useEffect(() => {
    analytics.trackPageView(location.pathname);
  }, [location.pathname]);

  // Return analytics methods for component use
  const trackEvent = useCallback((event: string, props?: Record<string, string | number>) => {
    analytics.track(event, props);
  }, []);

  const trackProjectView = useCallback((projectId: string, projectTitle: string) => {
    analytics.trackProjectView(projectId, projectTitle);
  }, []);

  const trackContactFormSubmit = useCallback(() => {
    analytics.trackContactFormSubmit();
  }, []);

  const trackResumeDownload = useCallback(() => {
    analytics.trackResumeDownload();
  }, []);

  const trackEasterEggFound = useCallback((easterEgg: string) => {
    analytics.trackEasterEggFound(easterEgg);
  }, []);

  const trackSocialClick = useCallback((platform: string) => {
    analytics.trackSocialClick(platform);
  }, []);

  const trackSkillInteraction = useCallback((skill: string) => {
    analytics.trackSkillInteraction(skill);
  }, []);

  const trackThemeToggle = useCallback((theme: string) => {
    analytics.trackThemeToggle(theme);
  }, []);

  const trackExternalLink = useCallback((url: string, context: string) => {
    analytics.trackExternalLink(url, context);
  }, []);

  return {
    trackEvent,
    trackProjectView,
    trackContactFormSubmit,
    trackResumeDownload,
    trackEasterEggFound,
    trackSocialClick,
    trackSkillInteraction,
    trackThemeToggle,
    trackExternalLink,
  };
};

// Hook for tracking component visibility
export const useTrackVisibility = (elementRef: React.RefObject<HTMLElement>, eventName: string) => {
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            analytics.track(eventName, {
              element_id: element.id || 'unknown',
              visibility_ratio: Math.round(entry.intersectionRatio * 100)
            });
          }
        });
      },
      { threshold: 0.5 }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [elementRef, eventName]);
};
