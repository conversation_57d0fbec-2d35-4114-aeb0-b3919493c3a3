import { useEffect, useState, useCallback } from 'react';

const KONAMI_CODE = [
  'ArrowUp',
  'ArrowUp',
  'ArrowDown',
  'ArrowDown',
  '<PERSON>Left',
  'ArrowRight',
  '<PERSON>Left',
  'ArrowRight',
  'KeyB',
  'KeyA'
];

export const useKonamiCode = (callback: () => void) => {
  const [keys, setKeys] = useState<string[]>([]);

  const resetKeys = useCallback(() => {
    setKeys([]);
  }, []);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    setKeys(prevKeys => {
      const newKeys = [...prevKeys, event.code];
      
      // Keep only the last 10 keys (length of Konami code)
      if (newKeys.length > KONAMI_CODE.length) {
        newKeys.shift();
      }
      
      // Check if the sequence matches the Konami code
      if (newKeys.length === KONAMI_CODE.length) {
        const matches = newKeys.every((key, index) => key === KONAMI_CODE[index]);
        if (matches) {
          callback();
          return []; // Reset after successful trigger
        }
      }
      
      return newKeys;
    });
  }, [callback]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  return { resetKeys };
};

// Alternative hook for sequence detection with custom codes
export const useKeySequence = (sequence: string[], callback: () => void) => {
  const [keys, setKeys] = useState<string[]>([]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    setKeys(prevKeys => {
      const newKeys = [...prevKeys, event.code];
      
      if (newKeys.length > sequence.length) {
        newKeys.shift();
      }
      
      if (newKeys.length === sequence.length) {
        const matches = newKeys.every((key, index) => key === sequence[index]);
        if (matches) {
          callback();
          return [];
        }
      }
      
      return newKeys;
    });
  }, [sequence, callback]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
};
