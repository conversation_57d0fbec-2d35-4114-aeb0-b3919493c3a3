import { useEffect, useState } from 'react';

/**
 * Hook to detect if user prefers reduced motion
 * Returns true if user has set prefers-reduced-motion: reduce
 */
export const useReducedMotion = () => {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
};

/**
 * Hook to get optimized animation variants based on user preferences
 */
export const useAnimationVariants = () => {
  const prefersReducedMotion = useReducedMotion();

  const getVariants = (normalVariants: any, reducedVariants?: any) => {
    if (prefersReducedMotion) {
      return reducedVariants || {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 },
        transition: { duration: 0.1 }
      };
    }
    return normalVariants;
  };

  const getTransition = (normalTransition: any) => {
    if (prefersReducedMotion) {
      return { duration: 0.1 };
    }
    return normalTransition;
  };

  return { getVariants, getTransition, prefersReducedMotion };
};
