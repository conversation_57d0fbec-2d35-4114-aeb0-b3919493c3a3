import { useEffect, useRef, useState } from 'react';

interface TouchGestureOptions {
  onSwipeLeft?: () => void;
  onSwipeRight?: () => void;
  onSwipeUp?: () => void;
  onSwipeDown?: () => void;
  onPinch?: (scale: number) => void;
  onTap?: () => void;
  onDoubleTap?: () => void;
  onLongPress?: () => void;
  threshold?: number;
  longPressDelay?: number;
}

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export const useTouchGestures = (options: TouchGestureOptions = {}) => {
  const {
    onSwipeLeft,
    onSwipeRight,
    onSwipeUp,
    onSwipeDown,
    onPinch,
    onTap,
    onDoubleTap,
    onLongPress,
    threshold = 50,
    longPressDelay = 500
  } = options;

  const elementRef = useRef<HTMLElement>(null);
  const [touchStart, setTouchStart] = useState<TouchPoint | null>(null);
  const [lastTap, setLastTap] = useState<number>(0);
  const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(null);
  const [initialDistance, setInitialDistance] = useState<number>(0);

  const getDistance = (touch1: Touch, touch2: Touch): number => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  const handleTouchStart = (e: TouchEvent) => {
    const touch = e.touches[0];
    const now = Date.now();

    setTouchStart({
      x: touch.clientX,
      y: touch.clientY,
      timestamp: now
    });

    // Handle multi-touch for pinch gestures
    if (e.touches.length === 2) {
      const distance = getDistance(e.touches[0], e.touches[1]);
      setInitialDistance(distance);
    }

    // Start long press timer
    if (onLongPress) {
      const timer = setTimeout(() => {
        onLongPress();
      }, longPressDelay);
      setLongPressTimer(timer);
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    // Cancel long press on move
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // Handle pinch gesture
    if (e.touches.length === 2 && onPinch && initialDistance > 0) {
      const currentDistance = getDistance(e.touches[0], e.touches[1]);
      const scale = currentDistance / initialDistance;
      onPinch(scale);
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    // Clear long press timer
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    if (!touchStart || e.touches.length > 0) return;

    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - touchStart.x;
    const deltaY = touch.clientY - touchStart.y;
    const deltaTime = Date.now() - touchStart.timestamp;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);

    // Handle tap gestures
    if (distance < threshold && deltaTime < 300) {
      const now = Date.now();
      const timeSinceLastTap = now - lastTap;

      if (timeSinceLastTap < 300 && onDoubleTap) {
        // Double tap
        onDoubleTap();
        setLastTap(0);
      } else {
        // Single tap
        if (onTap) {
          onTap();
        }
        setLastTap(now);
      }
      return;
    }

    // Handle swipe gestures
    if (distance >= threshold) {
      const absX = Math.abs(deltaX);
      const absY = Math.abs(deltaY);

      if (absX > absY) {
        // Horizontal swipe
        if (deltaX > 0 && onSwipeRight) {
          onSwipeRight();
        } else if (deltaX < 0 && onSwipeLeft) {
          onSwipeLeft();
        }
      } else {
        // Vertical swipe
        if (deltaY > 0 && onSwipeDown) {
          onSwipeDown();
        } else if (deltaY < 0 && onSwipeUp) {
          onSwipeUp();
        }
      }
    }

    setTouchStart(null);
    setInitialDistance(0);
  };

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    // Add passive listeners for better performance
    element.addEventListener('touchstart', handleTouchStart, { passive: true });
    element.addEventListener('touchmove', handleTouchMove, { passive: true });
    element.addEventListener('touchend', handleTouchEnd, { passive: true });

    return () => {
      element.removeEventListener('touchstart', handleTouchStart);
      element.removeEventListener('touchmove', handleTouchMove);
      element.removeEventListener('touchend', handleTouchEnd);
    };
  }, [touchStart, lastTap, longPressTimer, initialDistance]);

  return elementRef;
};

// Hook for detecting device capabilities
export const useDeviceCapabilities = () => {
  const [capabilities, setCapabilities] = useState({
    isTouchDevice: false,
    hasHover: false,
    isHighDPI: false,
    screenSize: 'desktop' as 'mobile' | 'tablet' | 'desktop',
    orientation: 'portrait' as 'portrait' | 'landscape',
    connectionSpeed: 'fast' as 'slow' | 'medium' | 'fast'
  });

  useEffect(() => {
    const updateCapabilities = () => {
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const hasHover = window.matchMedia('(hover: hover)').matches;
      const isHighDPI = window.devicePixelRatio > 1;
      
      // Determine screen size
      const width = window.innerWidth;
      let screenSize: 'mobile' | 'tablet' | 'desktop' = 'desktop';
      if (width < 768) screenSize = 'mobile';
      else if (width < 1024) screenSize = 'tablet';

      // Determine orientation
      const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';

      // Determine connection speed (if available)
      let connectionSpeed: 'slow' | 'medium' | 'fast' = 'fast';
      const connection = (navigator as any).connection;
      if (connection) {
        if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
          connectionSpeed = 'slow';
        } else if (connection.effectiveType === '3g') {
          connectionSpeed = 'medium';
        }
      }

      setCapabilities({
        isTouchDevice,
        hasHover,
        isHighDPI,
        screenSize,
        orientation,
        connectionSpeed
      });
    };

    updateCapabilities();

    // Listen for changes
    window.addEventListener('resize', updateCapabilities);
    window.addEventListener('orientationchange', updateCapabilities);

    return () => {
      window.removeEventListener('resize', updateCapabilities);
      window.removeEventListener('orientationchange', updateCapabilities);
    };
  }, []);

  return capabilities;
};

// Hook for mobile-specific animations
export const useMobileAnimations = () => {
  const { isTouchDevice, screenSize, connectionSpeed } = useDeviceCapabilities();

  const getOptimizedVariants = (normalVariants: any, mobileVariants?: any) => {
    if (isTouchDevice && mobileVariants) {
      return mobileVariants;
    }
    
    // Reduce animation complexity on slow connections
    if (connectionSpeed === 'slow') {
      return {
        initial: { opacity: 0 },
        animate: { opacity: 1 },
        exit: { opacity: 0 }
      };
    }

    return normalVariants;
  };

  const getOptimizedTransition = (normalTransition: any) => {
    if (isTouchDevice || connectionSpeed === 'slow') {
      return { duration: 0.2 };
    }
    return normalTransition;
  };

  return {
    isTouchDevice,
    screenSize,
    connectionSpeed,
    getOptimizedVariants,
    getOptimizedTransition
  };
};
