import { useState, useEffect } from 'react';
import { motion, AnimatePresence, PanInfo } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Menu, X, Home, User, Briefcase, Mail, Award } from 'lucide-react';
import { useTouchGestures, useDeviceCapabilities } from '@/hooks/use-touch-gestures';
import { useAnalytics } from '@/hooks/use-analytics';

interface NavItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  href: string;
}

const navItems: NavItem[] = [
  { id: 'hero', label: 'Home', icon: <Home className="w-5 h-5" />, href: '#hero' },
  { id: 'about', label: 'About', icon: <User className="w-5 h-5" />, href: '#about' },
  { id: 'projects', label: 'Projects', icon: <Briefcase className="w-5 h-5" />, href: '#projects' },
  { id: 'skills', label: 'Skills', icon: <Award className="w-5 h-5" />, href: '#skills' },
  { id: 'contact', label: 'Contact', icon: <Mail className="w-5 h-5" />, href: '#contact' }
];

const MobileNavigation = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('hero');
  const { isTouchDevice, screenSize } = useDeviceCapabilities();
  const { trackEvent } = useAnalytics();

  // Touch gestures for swipe to close
  const gestureRef = useTouchGestures({
    onSwipeLeft: () => {
      if (isOpen) {
        setIsOpen(false);
        trackEvent('mobile_nav_swipe_close');
      }
    },
    onTap: () => {
      if (isOpen) {
        setIsOpen(false);
      }
    }
  });

  // Track active section based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const sections = navItems.map(item => document.getElementById(item.id));
      const scrollPosition = window.scrollY + 100;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = sections[i];
        if (section && section.offsetTop <= scrollPosition) {
          setActiveSection(navItems[i].id);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
      setIsOpen(false);
      trackEvent('mobile_nav_click', { section: sectionId });
    }
  };

  const handleDragEnd = (event: any, info: PanInfo) => {
    if (info.offset.x < -100) {
      setIsOpen(false);
    }
  };

  // Only show on mobile devices
  if (!isTouchDevice || screenSize === 'desktop') {
    return null;
  }

  return (
    <>
      {/* Mobile Menu Button */}
      <motion.div
        className="fixed top-4 right-4 z-50 md:hidden"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 0.5, type: "spring", stiffness: 300 }}
      >
        <Button
          variant="default"
          size="icon"
          onClick={() => setIsOpen(!isOpen)}
          className="w-12 h-12 rounded-full shadow-lg bg-primary hover:bg-primary/90"
        >
          <motion.div
            animate={{ rotate: isOpen ? 180 : 0 }}
            transition={{ duration: 0.3 }}
          >
            {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
          </motion.div>
        </Button>
      </motion.div>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={gestureRef}
            className="fixed inset-0 bg-black/50 z-40 md:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Mobile Navigation Panel */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed top-0 right-0 h-full w-80 max-w-[80vw] bg-background border-l border-border z-50 md:hidden"
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            drag="x"
            dragConstraints={{ left: 0, right: 0 }}
            dragElastic={0.2}
            onDragEnd={handleDragEnd}
          >
            {/* Header */}
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-bold text-foreground">Navigation</h2>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setIsOpen(false)}
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
              <p className="text-sm text-muted-foreground mt-2">
                Swipe left or tap outside to close
              </p>
            </div>

            {/* Navigation Items */}
            <div className="p-6">
              <nav className="space-y-2">
                {navItems.map((item, index) => (
                  <motion.button
                    key={item.id}
                    onClick={() => scrollToSection(item.id)}
                    className={`w-full flex items-center gap-4 p-4 rounded-lg text-left transition-colors ${
                      activeSection === item.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-muted text-foreground'
                    }`}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <motion.div
                      animate={{
                        scale: activeSection === item.id ? 1.2 : 1,
                        rotate: activeSection === item.id ? 360 : 0
                      }}
                      transition={{ duration: 0.3 }}
                    >
                      {item.icon}
                    </motion.div>
                    <span className="font-medium">{item.label}</span>
                    {activeSection === item.id && (
                      <motion.div
                        className="ml-auto w-2 h-2 bg-current rounded-full"
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      />
                    )}
                  </motion.button>
                ))}
              </nav>
            </div>

            {/* Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-6 border-t border-border">
              <div className="text-center">
                <p className="text-sm text-muted-foreground mb-2">
                  Ready to work together?
                </p>
                <Button
                  onClick={() => scrollToSection('contact')}
                  className="w-full"
                  size="sm"
                >
                  Get In Touch
                </Button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bottom Navigation Bar (Alternative) */}
      {isTouchDevice && screenSize === 'mobile' && (
        <motion.div
          className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-md border-t border-border z-40 md:hidden"
          initial={{ y: 100 }}
          animate={{ y: 0 }}
          transition={{ delay: 1, type: "spring", stiffness: 300 }}
        >
          <div className="flex items-center justify-around py-2">
            {navItems.slice(0, 5).map((item) => (
              <motion.button
                key={item.id}
                onClick={() => scrollToSection(item.id)}
                className={`flex flex-col items-center gap-1 p-2 rounded-lg transition-colors ${
                  activeSection === item.id
                    ? 'text-primary'
                    : 'text-muted-foreground'
                }`}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <motion.div
                  animate={{
                    scale: activeSection === item.id ? 1.2 : 1,
                    y: activeSection === item.id ? -2 : 0
                  }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  {item.icon}
                </motion.div>
                <span className="text-xs font-medium">{item.label}</span>
                {activeSection === item.id && (
                  <motion.div
                    className="w-1 h-1 bg-primary rounded-full"
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  />
                )}
              </motion.button>
            ))}
          </div>
        </motion.div>
      )}
    </>
  );
};

export default MobileNavigation;
