import { Card, CardContent } from '@/components/ui/card';
import { Quote } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    quote: "<PERSON><PERSON> is that rare developer who combines technical prowess with a business-first mindset.",
    author: "<PERSON>",
    role: "Product Manager",
    company: "StartCo"
  },
  {
    id: 2,
    quote: "His ability to understand complex healthcare workflows and translate them into elegant code is remarkable.",
    author: "Dr. <PERSON>",
    role: "CTO",
    company: "MedTech Solutions"
  },
  {
    id: 3,
    quote: "<PERSON><PERSON> delivered our real estate platform ahead of schedule and it's been generating consistent revenue since launch.",
    author: "<PERSON>",
    role: "Founder",
    company: "PropNext"
  },
  {
    id: 4,
    quote: "Working with <PERSON><PERSON> was like having a co-founder who could code. He thinks strategically about every feature.",
    author: "<PERSON>",
    role: "CEO",
    company: "TechVenture"
  }
];

const TestimonialsSection = () => {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-amber-50 to-orange-50 dark:from-amber-950/30 dark:to-orange-950/30 rounded-full border border-amber-200/50 dark:border-amber-800/50 mb-8">
            <div className="w-3 h-3 bg-amber-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-semibold text-amber-700 dark:text-amber-300">Client Success Stories</span>
          </div>

          <h2 className="text-5xl lg:text-7xl font-black text-foreground mb-6 leading-tight">
            <span className="block">Trusted by</span>
            <span className="block bg-gradient-to-r from-amber-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
              Industry Leaders
            </span>
          </h2>

          <p className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-medium">
            Delivering exceptional results for healthcare organizations, fintech startups,
            and real estate companies across 3 continents with proven track record.
          </p>

          {/* Trust Metrics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-12 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">60%</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Efficiency Gains</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">4 Weeks</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Fastest Delivery</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">15+</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Engineers Mentored</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">₦200K+</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">GMV Generated</div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="relative overflow-hidden group hover:shadow-portfolio transition-all duration-300">
              <CardContent className="p-8">
                <Quote className="h-8 w-8 text-primary mb-4 opacity-50" />
                
                <blockquote className="text-lg text-foreground mb-6 italic leading-relaxed">
                  "{testimonial.quote}"
                </blockquote>
                
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center text-primary-foreground font-semibold">
                    {testimonial.author.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-semibold text-foreground">{testimonial.author}</div>
                    <div className="text-sm text-muted-foreground">
                      {testimonial.role} at {testimonial.company}
                    </div>
                  </div>
                </div>

                {/* Decorative element */}
                <div className="absolute top-0 right-0 w-20 h-20 bg-primary/5 rounded-bl-full group-hover:bg-primary/10 transition-colors duration-300"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Large testimonial card */}
        <Card className="bg-primary text-primary-foreground max-w-4xl mx-auto mt-12">
          <CardContent className="p-12 text-center">
            <Quote className="h-12 w-12 mx-auto mb-6 opacity-70" />
            <blockquote className="text-2xl lg:text-3xl font-medium mb-8 italic leading-relaxed">
              "I'm Rainer, a full-stack developer based in Port Harcourt, Nigeria. 
              I specialize in crafting high-performance applications that solve 
              complex problems and drive business growth."
            </blockquote>
            <div className="text-primary-foreground/80 text-lg">
              — Rainer, Full-Stack Developer
            </div>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default TestimonialsSection;