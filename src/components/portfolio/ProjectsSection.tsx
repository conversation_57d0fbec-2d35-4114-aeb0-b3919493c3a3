import { Link } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Github, ArrowRight } from 'lucide-react';

interface Project {
  id: string;
  title: string;
  description: string;
  problem: string;
  solution: string;
  outcome: string;
  tech: string[];
  image?: string;
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent',
    description: 'A rental platform for Port Harcourt, increasing user base by 150%',
    problem: 'Created a rental platform for Port Harcourt, increasing user base by 150%',
    solution: 'Built comprehensive rental management system with search, booking, and payment integration',
    outcome: 'Controlled rental story for rental crash and planning',
    tech: ['React', 'Node.js', 'PostgreSQL', 'Stripe'],
    featured: true
  },
  {
    id: 'medequip',
    title: 'MedEquip',
    description: 'Hospital equipment management system reducing costs by 50%',
    problem: 'Hospitals struggled with equipment tracking and maintenance scheduling',
    solution: 'Successfully managed hospital equipment across Nigeria, cutting 50%',
    outcome: 'Implemented real-time data with baseline stack, online tracking system',
    tech: ['React', 'Express', 'MongoDB', 'Chart.js'],
    featured: true
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite',
    description: 'Healthcare dashboard reducing reporting time by 35%',
    problem: 'Healthcare dashboard with real-time data and reducing reporting by 35%',
    solution: 'Built comprehensive health management platform with analytics and reporting',
    outcome: 'Reduced reporting delays by 58%',
    tech: ['Next.js', 'TypeScript', 'PostgreSQL', 'D3.js'],
    featured: true
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet',
    description: 'Secure cryptocurrency wallet with multi-chain support',
    problem: 'Users needed secure, easy-to-use wallet for multiple cryptocurrencies',
    solution: 'Developed wallet with biometric security and cross-chain transactions',
    outcome: 'Processed over $2M in secure transactions',
    tech: ['React Native', 'Web3.js', 'Solidity', 'Node.js'],
    featured: false
  }
];

const ProjectsSection = () => {
  return (
    <section id="projects" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Case Studies
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Real projects that solved complex problems and delivered measurable business impact
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
          {projects.filter(p => p.featured).map((project) => (
            <Card key={project.id} className="group hover:shadow-portfolio transition-all duration-300 border-0 shadow-lg">
              <CardHeader className="pb-4">
                <CardTitle className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                  {project.title}
                </CardTitle>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Problem</h4>
                    <p className="text-muted-foreground text-sm">{project.problem}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Solution</h4>
                    <p className="text-muted-foreground text-sm">{project.solution}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Outcome</h4>
                    <p className="text-muted-foreground text-sm">{project.outcome}</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech) => (
                    <Badge key={tech} variant="secondary" className="text-xs">
                      {tech}
                    </Badge>
                  ))}
                </div>

                <div className="flex gap-2 pt-4">
                  <Link to={`/project/${project.id}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full">
                      View Details
                    </Button>
                  </Link>
                  {project.liveUrl && (
                    <Button asChild size="sm" className="flex-1 bg-primary">
                      <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4 mr-2" />
                        Live Demo
                      </a>
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Link to="/projects">
            <Button variant="outline" size="lg" className="group">
              View All Projects
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default ProjectsSection;