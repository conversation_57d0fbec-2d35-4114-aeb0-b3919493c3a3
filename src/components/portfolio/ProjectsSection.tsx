import { Link } from 'react-router-dom';
import { useState } from 'react';
import { motion, PanInfo } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Github, ArrowRight, ChevronLeft, ChevronRight, Award } from 'lucide-react';
import { useTouchGestures, useDeviceCapabilities } from '@/hooks/use-touch-gestures';

interface Project {
  id: string;
  title: string;
  description: string;
  problem: string;
  solution: string;
  outcome: string;
  tech: string[];
  image?: string;
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
}

const projects: Project[] = [
  {
    id: 'phcityrent',
    title: 'PHCityRent - Real Estate & Local Services',
    description: 'Revolutionary rental platform with verified agents, escrow booking, and home services. Achieved 40+ rentals with ₦200k+ GMV.',
    problem: 'Rental search in Port Harcourt lacked trust, speed, and verified agents',
    solution: 'Built full app architecture with React Native, Node.js, Firebase, and Paystack. Pioneered escrow-based booking via WhatsApp.',
    outcome: '40+ completed rentals, ₦200k+ GMV, verified home services revenue stream',
    tech: ['React Native', 'Node.js', 'Firebase', 'Paystack', 'TailwindCSS'],
    image: '/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true
  },
  {
    id: 'medequip',
    title: 'MedEquip - Health Logistics Dashboard',
    description: 'Dynamic healthcare dashboard reducing asset tracking time by 60%. Modular components reused across 5+ projects.',
    problem: 'Manual tracking of medical equipment across hospitals led to downtime and loss',
    solution: 'Built dynamic dashboard with Next.js featuring sourcing, inventory, and real-time analytics',
    outcome: '60% faster asset tracking, improved inter-hospital communication, 5+ project reuse',
    tech: ['Next.js', 'React', 'Tailwind', 'Node.js', 'Firebase'],
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true
  },
  {
    id: 'infinity-health',
    title: 'Infinity Health Suite - Data Visualization',
    description: 'Interactive healthcare dashboard with D3.js visualizations. 35% faster data interpretation across 3+ hospitals.',
    problem: 'Doctors and researchers struggled with interpreting complex patient data visually',
    solution: 'Built interactive dashboards with React, Redux, D3.js, and TikZ/PGF for diagnostic clarity',
    outcome: '35% faster data interpretation, deployed across 3+ hospitals and 1 research center',
    tech: ['React', 'Redux', 'D3.js', 'TikZ/PGF', 'TypeScript'],
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true
  },
  {
    id: 'krypto-wallet',
    title: 'Krypto Wallet - Web3 Platform',
    description: 'Secure crypto wallet with clean UX. Delivered feature-complete in 4 weeks despite market turbulence.',
    problem: 'Clients needed secure crypto wallet with clean UX in unstable market',
    solution: 'Built core UI/UX with React, connected smart contracts with Solidity and Ethers.js',
    outcome: 'Secure onboarding and token swaps, 4-week delivery despite market volatility',
    tech: ['Solidity', 'React', 'Metamask', 'Smart Contracts', 'Ethers.js'],
    githubUrl: 'https://github.com/rainernsa/krypto-wallet',
    featured: false
  }
];

const ProjectsSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const { isTouchDevice, screenSize } = useDeviceCapabilities();
  const featuredProjects = projects.filter(p => p.featured);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredProjects.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredProjects.length) % featuredProjects.length);
  };

  const handleDragEnd = (event: any, info: PanInfo) => {
    if (info.offset.x > 100) {
      prevSlide();
    } else if (info.offset.x < -100) {
      nextSlide();
    }
  };

  // Touch gestures for mobile
  const gestureRef = useTouchGestures({
    onSwipeLeft: nextSlide,
    onSwipeRight: prevSlide
  });

  return (
    <section id="projects" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 rounded-full border border-blue-200/50 dark:border-blue-800/50 mb-8">
            <Award className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">Enterprise Portfolio</span>
          </div>

          <h2 className="text-5xl lg:text-7xl font-black text-foreground mb-6 leading-tight">
            <span className="block">Featured</span>
            <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent">
              Solutions
            </span>
          </h2>

          <p className="text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed font-medium">
            Discover real-world solutions that have generated ₦200K+ GMV, reduced operational costs by 60%,
            and transformed healthcare, real estate, and fintech across 3 continents.
          </p>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mt-12 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">4+</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Major Projects</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">₦200K+</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">GMV Generated</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">60%</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Efficiency Gains</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-black text-foreground mb-2">3</div>
              <div className="text-sm font-medium text-muted-foreground uppercase tracking-wide">Continents</div>
            </div>
          </div>
        </motion.div>

        {/* Mobile Carousel */}
        {isTouchDevice && screenSize === 'mobile' ? (
          <div className="relative mb-12">
            <div
              ref={gestureRef}
              className="overflow-hidden rounded-lg"
            >
              <motion.div
                className="flex"
                animate={{ x: `-${currentSlide * 100}%` }}
                transition={{ type: "spring", stiffness: 300, damping: 30 }}
                drag="x"
                dragConstraints={{ left: 0, right: 0 }}
                dragElastic={0.2}
                onDragEnd={handleDragEnd}
              >
                {featuredProjects.map((project, index) => (
                  <div key={project.id} className="w-full flex-shrink-0 px-2">
                    <Card className="h-full hover:shadow-portfolio transition-all duration-300 border-0 shadow-lg overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-2 mb-4">
                          <Badge variant="secondary" className="text-xs">Featured</Badge>
                          <Badge variant="outline" className="text-xs">{project.category}</Badge>
                        </div>

                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <CardTitle className="text-xl font-bold text-foreground group-hover:text-primary transition-colors mb-3">
                            {project.title}
                          </CardTitle>
                        </motion.div>

                        <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                          {project.description}
                        </p>

                        <div className="flex flex-wrap gap-2 mb-4">
                          {project.tech.slice(0, 3).map((tech, techIndex) => (
                            <motion.div
                              key={tech}
                              whileHover={{ scale: 1.1, rotate: 2 }}
                              transition={{ type: "spring", stiffness: 300 }}
                            >
                              <Badge variant="secondary" className="text-xs cursor-pointer">
                                {tech}
                              </Badge>
                            </motion.div>
                          ))}
                          {project.tech.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{project.tech.length - 3}
                            </Badge>
                          )}
                        </div>

                        <div className="flex gap-2">
                          <Link to={`/project/${project.id}`} className="flex-1">
                            <Button variant="outline" size="sm" className="w-full">
                              View Details
                            </Button>
                          </Link>
                          {project.liveUrl && (
                            <Button asChild size="sm" className="flex-1 bg-primary">
                              <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                                <ExternalLink className="h-4 w-4 mr-2" />
                                Demo
                              </a>
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </motion.div>
            </div>

            {/* Navigation buttons */}
            <div className="flex items-center justify-between mt-4">
              <Button
                variant="outline"
                size="icon"
                onClick={prevSlide}
                disabled={currentSlide === 0}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              {/* Dots indicator */}
              <div className="flex gap-2">
                {featuredProjects.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentSlide ? 'bg-primary' : 'bg-muted'
                    }`}
                  />
                ))}
              </div>

              <Button
                variant="outline"
                size="icon"
                onClick={nextSlide}
                disabled={currentSlide === featuredProjects.length - 1}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        ) : (
          /* Desktop Grid */
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {projects.filter(p => p.featured).map((project, index) => (
            <motion.div
              key={project.id}
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              whileHover={{ y: -10 }}
              className="group"
            >
              <Card className="h-full hover:shadow-portfolio transition-all duration-300 border-0 shadow-lg overflow-hidden">
              <CardHeader className="pb-4">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <CardTitle className="text-2xl font-bold text-foreground group-hover:text-primary transition-colors">
                    {project.title}
                  </CardTitle>
                </motion.div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Problem</h4>
                    <p className="text-muted-foreground text-sm">{project.problem}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Solution</h4>
                    <p className="text-muted-foreground text-sm">{project.solution}</p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-primary mb-2">Outcome</h4>
                    <p className="text-muted-foreground text-sm">{project.outcome}</p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2">
                  {project.tech.map((tech, techIndex) => (
                    <motion.div
                      key={tech}
                      whileHover={{ scale: 1.1, rotate: 2 }}
                      transition={{ type: "spring", stiffness: 300 }}
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      style={{ transitionDelay: `${techIndex * 0.1}s` }}
                    >
                      <Badge variant="secondary" className="text-xs cursor-pointer">
                        {tech}
                      </Badge>
                    </motion.div>
                  ))}
                </div>

                <div className="flex gap-2 pt-4">
                  <Link to={`/project/${project.id}`} className="flex-1">
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <Button variant="outline" size="sm" className="w-full">
                        View Details
                      </Button>
                    </motion.div>
                  </Link>
                  {project.liveUrl && (
                    <motion.div
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      transition={{ type: "spring", stiffness: 300 }}
                      className="flex-1"
                    >
                      <Button asChild size="sm" className="w-full bg-primary">
                        <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                          <motion.div
                            animate={{ x: [0, 3, 0] }}
                            transition={{ duration: 2, repeat: Infinity }}
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                          </motion.div>
                          Live Demo
                        </a>
                      </Button>
                    </motion.div>
                  )}
                </div>
              </CardContent>
            </Card>
            </motion.div>
          ))}
          </div>
        )}

        {/* Enterprise CTA Section */}
        <motion.div
          className="text-center mt-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-to-r from-slate-50 to-blue-50 dark:from-slate-900 dark:to-blue-900 rounded-3xl p-12 max-w-4xl mx-auto border border-slate-200 dark:border-slate-700">
            <div className="mb-8">
              <h3 className="text-3xl lg:text-4xl font-black text-foreground mb-4">
                Explore Our Complete
                <span className="block bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Project Portfolio
                </span>
              </h3>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto leading-relaxed">
                Discover real-world solutions across healthcare, fintech, and real estate.
                Each project showcases technical excellence, rapid delivery, and measurable business impact.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
              <Link to="/projects">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Button
                    size="xl"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-10 py-5 text-lg font-bold rounded-2xl shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 border-0"
                  >
                    View Complete Portfolio
                    <motion.div
                      className="ml-3"
                      animate={{ x: [0, 5, 0] }}
                      transition={{ duration: 2, repeat: Infinity }}
                    >
                      <ArrowRight className="h-6 w-6" />
                    </motion.div>
                  </Button>
                </motion.div>
              </Link>

              <div className="flex items-center gap-6 text-sm text-muted-foreground">
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>4+ Major Projects</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <span>₦200K+ GMV</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                  <span>3 Continents</span>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProjectsSection;