import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Card, CardContent } from '@/components/ui/card';
import LazyImage from '@/components/ui/lazy-image';

gsap.registerPlugin(ScrollTrigger);

const AboutSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const statsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate content on scroll
      gsap.fromTo(contentRef.current?.children || [],
        {
          opacity: 0,
          y: 50
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: contentRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate image with parallax effect
      gsap.fromTo(imageRef.current,
        {
          opacity: 0,
          scale: 1.1
        },
        {
          opacity: 1,
          scale: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: imageRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate stats with counter effect
      gsap.fromTo(statsRef.current?.children || [],
        {
          opacity: 0,
          y: 30,
          scale: 0.8
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.1,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: statsRef.current,
            start: "top 85%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Counter animation for numbers
      const numbers = statsRef.current?.querySelectorAll('.stat-number');
      numbers?.forEach((number) => {
        const finalValue = number.textContent || '0';
        const numericValue = parseInt(finalValue.replace(/\D/g, ''));

        gsap.fromTo(number,
          { textContent: 0 },
          {
            textContent: numericValue,
            duration: 2,
            ease: "power2.out",
            snap: { textContent: 1 },
            scrollTrigger: {
              trigger: number,
              start: "top 85%",
              toggleActions: "play none none reverse"
            },
            onUpdate: function() {
              const current = Math.round(this.targets()[0].textContent);
              if (finalValue.includes('+')) {
                number.textContent = current + '+';
              } else if (finalValue.includes('M')) {
                number.textContent = '$' + current + 'M+';
              } else {
                number.textContent = current.toString();
              }
            }
          }
        );
      });

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section id="about" ref={sectionRef} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div ref={contentRef} className="space-y-8">
            <div>
              <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
                About
              </h2>
              <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                <p>
                  I'm Rainer, a full-stack developer based in Port Harcourt, Nigeria. 
                  I specialize in crafting high-performance applications that solve 
                  complex problems and drive business growth.
                </p>
                <p>
                  My journey into development started with a passion for creating 
                  solutions that make a real difference. From healthcare platforms 
                  to real estate systems, I focus on building products that not only 
                  work flawlessly but also deliver measurable business impact.
                </p>
                <p>
                  As a product-minded developer, I don't just write code—I think 
                  strategically about user experience, business goals, and scalable 
                  architecture. I've mentored teams, shipped global applications, 
                  and am always open to co-founding the next big thing.
                </p>
              </div>
            </div>

            {/* Fun Facts */}
            <Card className="bg-muted/50">
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-4">Fun Facts</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>🏀 Basketball enthusiast and weekend player</li>
                  <li>🎯 Mentored 15+ junior developers</li>
                  <li>🚀 Open to co-founding opportunities</li>
                  <li>📚 Always learning new technologies</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Right Content - Image and Stats */}
          <div className="space-y-8">
            <div ref={imageRef} className="relative">
              <LazyImage
                src="/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png"
                alt="Rainer working on a project"
                className="w-full h-96 object-cover rounded-lg shadow-portfolio"
                containerClassName="w-full h-96 rounded-lg overflow-hidden"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
            </div>

            {/* Stats Grid */}
            <div ref={statsRef} className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="stat-number text-3xl font-bold text-primary mb-2">50+</div>
                  <div className="text-sm text-muted-foreground">Projects Delivered</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="stat-number text-3xl font-bold text-primary mb-2">5+</div>
                  <div className="text-sm text-muted-foreground">Years Experience</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="stat-number text-3xl font-bold text-primary mb-2">15+</div>
                  <div className="text-sm text-muted-foreground">Developers Mentored</div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6 text-center">
                  <div className="stat-number text-3xl font-bold text-primary mb-2">$2M+</div>
                  <div className="text-sm text-muted-foreground">Revenue Generated</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;