import { Card, CardContent } from '@/components/ui/card';

const AboutSection = () => {
  return (
    <section id="about" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6">
                About
              </h2>
              <div className="space-y-6 text-lg text-muted-foreground leading-relaxed">
                <p>
                  I'm <PERSON><PERSON>, a full-stack developer based in Port Harcourt, Nigeria. 
                  I specialize in crafting high-performance applications that solve 
                  complex problems and drive business growth.
                </p>
                <p>
                  My journey into development started with a passion for creating 
                  solutions that make a real difference. From healthcare platforms 
                  to real estate systems, I focus on building products that not only 
                  work flawlessly but also deliver measurable business impact.
                </p>
                <p>
                  As a product-minded developer, I don't just write code—I think 
                  strategically about user experience, business goals, and scalable 
                  architecture. I've mentored teams, shipped global applications, 
                  and am always open to co-founding the next big thing.
                </p>
              </div>
            </div>

            {/* Fun Facts */}
            <Card className="bg-muted/50">
              <CardContent className="p-6">
                <h3 className="font-semibold text-foreground mb-4">Fun Facts</h3>
                <ul className="space-y-2 text-muted-foreground">
                  <li>🏀 Basketball enthusiast and weekend player</li>
                  <li>🎯 Mentored 15+ junior developers</li>
                  <li>🚀 Open to co-founding opportunities</li>
                  <li>📚 Always learning new technologies</li>
                </ul>
              </CardContent>
            </Card>
          </div>

          {/* Right Content - Image and Stats */}
          <div className="space-y-8">
            <div className="relative">
              <img 
                src="/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png"
                alt="Rainer working on a project"
                className="w-full h-96 object-cover rounded-lg shadow-portfolio"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg"></div>
            </div>

            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-4">
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">50+</div>
                  <div className="text-sm text-muted-foreground">Projects Delivered</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">5+</div>
                  <div className="text-sm text-muted-foreground">Years Experience</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">15+</div>
                  <div className="text-sm text-muted-foreground">Developers Mentored</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-6 text-center">
                  <div className="text-3xl font-bold text-primary mb-2">$2M+</div>
                  <div className="text-sm text-muted-foreground">Revenue Generated</div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;