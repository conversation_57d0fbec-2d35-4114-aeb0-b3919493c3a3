import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, Download } from 'lucide-react';
import { useReducedMotion } from '@/hooks/use-reduced-motion';
import { commonVariants, commonTransitions } from '@/lib/animation-utils';
import LazyImage from '@/components/ui/lazy-image';

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const floatingElementsRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    const tl = gsap.timeline();

    // Set initial states
    gsap.set([titleRef.current, subtitleRef.current, ctaRef.current], {
      opacity: 0,
      y: 50
    });

    gsap.set(imageRef.current, {
      opacity: 0,
      scale: 0.8,
      rotation: -5
    });

    // Set floating elements initial state
    gsap.set(floatingElementsRef.current?.children || [], {
      opacity: 0,
      scale: 0,
      rotation: 0
    });

    // Set particles initial state
    gsap.set(particlesRef.current?.children || [], {
      opacity: 0,
      scale: 0
    });

    // Animate in sequence with enhanced effects
    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 1.2,
      ease: "power3.out"
    })
    .to(subtitleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.8")
    .to(ctaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6")
    .to(imageRef.current, {
      opacity: 1,
      scale: 1,
      rotation: 0,
      duration: 1.5,
      ease: "elastic.out(1, 0.6)"
    }, "-=1.0")
    .to(floatingElementsRef.current?.children || [], {
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: "back.out(1.7)",
      stagger: 0.2
    }, "-=0.8")
    .to(particlesRef.current?.children || [], {
      opacity: 0.6,
      scale: 1,
      duration: 0.8,
      ease: "power2.out",
      stagger: 0.1
    }, "-=0.6");

    // Continuous floating animation for elements
    gsap.to(floatingElementsRef.current?.children || [], {
      y: "random(-20, 20)",
      x: "random(-10, 10)",
      rotation: "random(-15, 15)",
      duration: "random(3, 5)",
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true,
      stagger: 0.3
    });

    // Continuous particle floating
    gsap.to(particlesRef.current?.children || [], {
      y: "random(-30, 30)",
      x: "random(-20, 20)",
      duration: "random(4, 8)",
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true,
      stagger: 0.2
    });

  }, []);

  return (
    <section id="hero" ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      {/* Floating Background Particles - Only if motion is not reduced */}
      {!prefersReducedMotion && (
        <div ref={particlesRef} className="absolute inset-0 pointer-events-none">
          {[...Array(12)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 bg-white/20 rounded-full"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                willChange: 'transform, opacity'
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.3, 0.8, 0.3],
              }}
              transition={{
                duration: Math.random() * 3 + 2,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      )}

      <div className="container mx-auto px-4 grid lg:grid-cols-2 gap-12 items-center relative z-10">
        {/* Left Content */}
        <div className="space-y-8">
          <div className="space-y-6">
            {/* Enterprise Status Badge */}
            <div className="inline-flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-emerald-500/20 to-blue-500/20 backdrop-blur-sm rounded-full border border-white/20 mb-8">
              <div className="relative">
                <div className="w-3 h-3 bg-emerald-400 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-emerald-400 rounded-full animate-ping opacity-75"></div>
              </div>
              <span className="text-sm font-semibold text-white">Available for Enterprise Projects</span>
            </div>

            <h1
              ref={titleRef}
              className="text-6xl lg:text-8xl font-black text-white leading-[0.9] tracking-tight mb-6"
            >
              <span className="block">Enterprise</span>
              <span className="block bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent">
                Solutions
              </span>
              <span className="block text-4xl lg:text-5xl font-bold text-white/80 mt-2">
                That Scale
              </span>
            </h1>

            <p
              ref={subtitleRef}
              className="text-xl lg:text-2xl text-white/90 leading-relaxed max-w-2xl font-medium"
            >
              Delivering Fortune 500-grade digital transformation through cutting-edge technology,
              strategic innovation, and measurable business impact.
            </p>
          </div>

          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Button
                size="lg"
                className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-10 py-5 text-lg font-bold rounded-2xl shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 border-0 w-full"
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Explore Portfolio
                <motion.div
                  className="ml-3"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight className="h-6 w-6" />
                </motion.div>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white/30 text-white hover:bg-white/10 hover:border-white backdrop-blur-sm px-10 py-5 text-lg font-bold rounded-2xl transition-all duration-300 w-full"
                asChild
              >
                <a href="/resume.pdf" download="Rayner_Resume.pdf">
                  <Download className="mr-3 h-6 w-6" />
                  Executive Summary
                </a>
              </Button>
            </motion.div>
          </div>

          {/* Enterprise Stats */}
          <div className="grid grid-cols-3 gap-8 pt-10 border-t border-white/20">
            <div className="text-center lg:text-left">
              <div className="text-4xl font-black text-white mb-1">$2M+</div>
              <div className="text-sm font-medium text-white/70 uppercase tracking-wide">Revenue Generated</div>
            </div>
            <div className="text-center lg:text-left">
              <div className="text-4xl font-black text-white mb-1">25+</div>
              <div className="text-sm font-medium text-white/70 uppercase tracking-wide">Enterprise Clients</div>
            </div>
            <div className="text-center lg:text-left">
              <div className="text-4xl font-black text-white mb-1">99.9%</div>
              <div className="text-sm font-medium text-white/70 uppercase tracking-wide">Uptime SLA</div>
            </div>
          </div>

          {/* Trust Indicators */}
          <div className="flex flex-wrap items-center gap-6 pt-8">
            <div className="flex items-center gap-2 text-sm font-medium text-white/80">
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              SOC 2 Compliant
            </div>
            <div className="flex items-center gap-2 text-sm font-medium text-white/80">
              <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              AWS Certified
            </div>
            <div className="flex items-center gap-2 text-sm font-medium text-white/80">
              <div className="w-5 h-5 bg-purple-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              24/7 Support
            </div>
          </div>
        </div>

        {/* Right Content - Profile Image */}
        <div ref={imageRef} className="flex justify-center lg:justify-end">
          <div className="relative">
            <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white/20 shadow-2xl">
              <LazyImage
                src="/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png"
                alt="Rainer - Full Stack Developer"
                className="w-full h-full object-cover"
                priority={true}
                containerClassName="w-full h-full"
              />
            </div>

            {/* Enhanced Floating elements */}
            <div ref={floatingElementsRef} className="absolute inset-0">
              <motion.div
                className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 10 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-2xl">💻</span>
              </motion.div>
              <motion.div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: -10 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-xl">🚀</span>
              </motion.div>
              <motion.div
                className="absolute top-1/2 -left-8 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-lg">⚡</span>
              </motion.div>
              <motion.div
                className="absolute top-8 right-8 w-14 h-14 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: -15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-lg">🎯</span>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;