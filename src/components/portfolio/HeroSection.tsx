import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { Button } from '@/components/ui/button';
import { ArrowRight, Download } from 'lucide-react';

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline();
    
    // Set initial states
    gsap.set([titleRef.current, subtitleRef.current, ctaRef.current], {
      opacity: 0,
      y: 50
    });
    
    gsap.set(imageRef.current, {
      opacity: 0,
      scale: 0.8
    });

    // Animate in sequence
    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 1,
      ease: "power3.out"
    })
    .to(subtitleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6")
    .to(ctaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.4")
    .to(imageRef.current, {
      opacity: 1,
      scale: 1,
      duration: 1.2,
      ease: "elastic.out(1, 0.8)"
    }, "-=0.8");

  }, []);

  return (
    <section id="hero" ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      <div className="container mx-auto px-4 grid lg:grid-cols-2 gap-12 items-center">
        {/* Left Content */}
        <div className="space-y-8">
          <div className="space-y-6">
            <h1 
              ref={titleRef}
              className="text-5xl lg:text-7xl font-bold text-white leading-tight"
            >
              I build full-stack apps that turn{' '}
              <span className="text-accent bg-gradient-to-r from-teal-light to-white bg-clip-text text-transparent">
                ideas into impact
              </span>
            </h1>
            
            <p 
              ref={subtitleRef}
              className="text-xl lg:text-2xl text-white/80 leading-relaxed max-w-lg"
            >
              Real-world products in health, real estate & AI that solve complex problems and drive business growth.
            </p>
          </div>

          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4">
            <Button 
              size="lg" 
              className="group bg-white text-teal-dark hover:bg-white/90 transition-all duration-300 shadow-portfolio"
              onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
            >
              View Projects
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button 
              variant="outline" 
              size="lg"
              className="border-white text-white hover:bg-white hover:text-teal-dark transition-all duration-300"
              onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
            >
              Contact Me
            </Button>
          </div>

          <div className="flex items-center gap-4 text-white/60">
            <div className="h-px bg-white/30 flex-1"></div>
            <span className="text-sm">Full-Stack Developer • Port Harcourt, Nigeria</span>
            <div className="h-px bg-white/30 flex-1"></div>
          </div>
        </div>

        {/* Right Content - Profile Image */}
        <div ref={imageRef} className="flex justify-center lg:justify-end">
          <div className="relative">
            <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white/20 shadow-2xl">
              <img 
                src="/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png"
                alt="Rainer - Full Stack Developer"
                className="w-full h-full object-cover"
              />
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center">
              <span className="text-2xl">💻</span>
            </div>
            <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center">
              <span className="text-xl">🚀</span>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;