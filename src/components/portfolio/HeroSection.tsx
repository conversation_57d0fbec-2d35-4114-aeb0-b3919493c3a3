import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { ArrowRight, Download } from 'lucide-react';

const HeroSection = () => {
  const heroRef = useRef<HTMLDivElement>(null);
  const titleRef = useRef<HTMLHeadingElement>(null);
  const subtitleRef = useRef<HTMLParagraphElement>(null);
  const ctaRef = useRef<HTMLDivElement>(null);
  const imageRef = useRef<HTMLDivElement>(null);
  const floatingElementsRef = useRef<HTMLDivElement>(null);
  const particlesRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const tl = gsap.timeline();

    // Set initial states
    gsap.set([titleRef.current, subtitleRef.current, ctaRef.current], {
      opacity: 0,
      y: 50
    });

    gsap.set(imageRef.current, {
      opacity: 0,
      scale: 0.8,
      rotation: -5
    });

    // Set floating elements initial state
    gsap.set(floatingElementsRef.current?.children || [], {
      opacity: 0,
      scale: 0,
      rotation: 0
    });

    // Set particles initial state
    gsap.set(particlesRef.current?.children || [], {
      opacity: 0,
      scale: 0
    });

    // Animate in sequence with enhanced effects
    tl.to(titleRef.current, {
      opacity: 1,
      y: 0,
      duration: 1.2,
      ease: "power3.out"
    })
    .to(subtitleRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.8")
    .to(ctaRef.current, {
      opacity: 1,
      y: 0,
      duration: 0.8,
      ease: "power3.out"
    }, "-=0.6")
    .to(imageRef.current, {
      opacity: 1,
      scale: 1,
      rotation: 0,
      duration: 1.5,
      ease: "elastic.out(1, 0.6)"
    }, "-=1.0")
    .to(floatingElementsRef.current?.children || [], {
      opacity: 1,
      scale: 1,
      duration: 0.6,
      ease: "back.out(1.7)",
      stagger: 0.2
    }, "-=0.8")
    .to(particlesRef.current?.children || [], {
      opacity: 0.6,
      scale: 1,
      duration: 0.8,
      ease: "power2.out",
      stagger: 0.1
    }, "-=0.6");

    // Continuous floating animation for elements
    gsap.to(floatingElementsRef.current?.children || [], {
      y: "random(-20, 20)",
      x: "random(-10, 10)",
      rotation: "random(-15, 15)",
      duration: "random(3, 5)",
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true,
      stagger: 0.3
    });

    // Continuous particle floating
    gsap.to(particlesRef.current?.children || [], {
      y: "random(-30, 30)",
      x: "random(-20, 20)",
      duration: "random(4, 8)",
      ease: "sine.inOut",
      repeat: -1,
      yoyo: true,
      stagger: 0.2
    });

  }, []);

  return (
    <section id="hero" ref={heroRef} className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-hero">
      {/* Floating Background Particles */}
      <div ref={particlesRef} className="absolute inset-0 pointer-events-none">
        {[...Array(12)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-white/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: Math.random() * 3 + 2,
              repeat: Infinity,
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <div className="container mx-auto px-4 grid lg:grid-cols-2 gap-12 items-center relative z-10">
        {/* Left Content */}
        <div className="space-y-8">
          <div className="space-y-6">
            <h1 
              ref={titleRef}
              className="text-5xl lg:text-7xl font-bold text-white leading-tight"
            >
              I build full-stack apps that turn{' '}
              <span className="bg-gradient-to-r from-teal-light to-white bg-clip-text text-transparent">
                ideas into impact
              </span>
            </h1>
            
            <p 
              ref={subtitleRef}
              className="text-xl lg:text-2xl text-white/80 leading-relaxed max-w-lg"
            >
              Real-world products in health, real estate & AI that solve complex problems and drive business growth.
            </p>
          </div>

          <div ref={ctaRef} className="flex flex-col sm:flex-row gap-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Button
                size="lg"
                className="group bg-white text-teal-dark hover:bg-white/90 transition-all duration-300 shadow-portfolio w-full"
                onClick={() => document.getElementById('projects')?.scrollIntoView({ behavior: 'smooth' })}
              >
                View Projects
                <motion.div
                  className="ml-2"
                  animate={{ x: [0, 5, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  <ArrowRight className="h-5 w-5" />
                </motion.div>
              </Button>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Button
                variant="outline"
                size="lg"
                className="border-white text-white hover:bg-white hover:text-teal-dark transition-all duration-300 w-full"
                onClick={() => document.getElementById('contact')?.scrollIntoView({ behavior: 'smooth' })}
              >
                Contact Me
              </Button>
            </motion.div>
          </div>

          <div className="flex items-center gap-4 text-white/60">
            <div className="h-px bg-white/30 flex-1"></div>
            <span className="text-sm">Full-Stack Developer • Port Harcourt, Nigeria</span>
            <div className="h-px bg-white/30 flex-1"></div>
          </div>
        </div>

        {/* Right Content - Profile Image */}
        <div ref={imageRef} className="flex justify-center lg:justify-end">
          <div className="relative">
            <div className="w-80 h-80 lg:w-96 lg:h-96 rounded-full overflow-hidden border-4 border-white/20 shadow-2xl">
              <img
                src="/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png"
                alt="Rainer - Full Stack Developer"
                className="w-full h-full object-cover"
              />
            </div>

            {/* Enhanced Floating elements */}
            <div ref={floatingElementsRef} className="absolute inset-0">
              <motion.div
                className="absolute -top-4 -right-4 w-20 h-20 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 10 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-2xl">💻</span>
              </motion.div>
              <motion.div
                className="absolute -bottom-4 -left-4 w-16 h-16 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: -10 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-xl">🚀</span>
              </motion.div>
              <motion.div
                className="absolute top-1/2 -left-8 w-12 h-12 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: 15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-lg">⚡</span>
              </motion.div>
              <motion.div
                className="absolute top-8 right-8 w-14 h-14 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center"
                whileHover={{ scale: 1.1, rotate: -15 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <span className="text-lg">🎯</span>
              </motion.div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/60 rounded-full mt-2"></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;