import { useEffect, useRef, useState } from 'react';
import { motion, useScroll, useTransform, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ExternalLink, Github, Calendar, TrendingUp, Users, Award } from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';
import LazyImage from '@/components/ui/lazy-image';

gsap.registerPlugin(ScrollTrigger);

interface ProjectMilestone {
  id: string;
  date: string;
  title: string;
  company: string;
  description: string;
  achievements: string[];
  technologies: string[];
  metrics?: {
    users?: string;
    revenue?: string;
    performance?: string;
    impact?: string;
  };
  image?: string;
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
}

const projectMilestones: ProjectMilestone[] = [
  {
    id: 'phcityrent-2024',
    date: '2024',
    title: 'PHCityRent Platform',
    company: 'Freelance Project',
    description: 'Revolutionary real estate platform transforming property rental in Port Harcourt with advanced search, virtual tours, and seamless booking.',
    achievements: [
      '150% user growth in first 6 months',
      'Reduced property search time by 70%',
      'Integrated virtual tour technology',
      'Built comprehensive admin dashboard'
    ],
    technologies: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Stripe'],
    metrics: {
      users: '10K+',
      revenue: '$50K+',
      performance: '98%',
      impact: 'High'
    },
    image: '/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png',
    liveUrl: 'https://phcityrent.com',
    githubUrl: 'https://github.com/rainernsa/phcityrent',
    featured: true
  },
  {
    id: 'medequip-2024',
    date: '2024',
    title: 'MedEquip Management System',
    company: 'Healthcare Startup',
    description: 'Comprehensive medical equipment management system reducing operational costs and improving efficiency for healthcare facilities.',
    achievements: [
      'Reduced equipment costs by 50%',
      'Improved maintenance scheduling by 80%',
      'Deployed across 15+ clinics',
      'Real-time inventory tracking'
    ],
    technologies: ['Next.js', 'TypeScript', 'Prisma', 'PostgreSQL', 'Vercel'],
    metrics: {
      users: '500+',
      revenue: '$25K+',
      performance: '95%',
      impact: 'High'
    },
    liveUrl: 'https://medequip.vercel.app',
    githubUrl: 'https://github.com/rainernsa/medequip',
    featured: true
  },
  {
    id: 'infinity-health-2023',
    date: '2023',
    title: 'Infinity Health Suite',
    company: 'Health Tech Startup',
    description: 'Advanced healthcare data management platform with powerful analytics and reporting capabilities.',
    achievements: [
      'Reduced reporting time by 58%',
      'Improved data accuracy by 40%',
      'Streamlined patient management',
      'Advanced analytics dashboard'
    ],
    technologies: ['Next.js', 'D3.js', 'PostgreSQL', 'Node.js', 'Chart.js'],
    metrics: {
      users: '1K+',
      revenue: '$15K+',
      performance: '92%',
      impact: 'Medium'
    },
    liveUrl: 'https://infinity-health-suite.vercel.app',
    githubUrl: 'https://github.com/rainernsa/infinity-health-suite',
    featured: true
  },
  {
    id: 'krypto-wallet-2023',
    date: '2023',
    title: 'Krypto Wallet Interface',
    company: 'Fintech Project',
    description: 'Modern cryptocurrency wallet interface with advanced security features and intuitive user experience.',
    achievements: [
      'Secure transaction processing',
      'Multi-currency support',
      'Advanced security features',
      'Intuitive user interface'
    ],
    technologies: ['React', 'Web3.js', 'Ethereum', 'TypeScript', 'Tailwind'],
    metrics: {
      users: '2K+',
      performance: '96%',
      impact: 'Medium'
    },
    githubUrl: 'https://github.com/rainernsa/krypto-wallet'
  }
];

const ProjectTimeline = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const timelineRef = useRef<HTMLDivElement>(null);
  const [activeIndex, setActiveIndex] = useState(0);
  const { trackEvent } = useAnalytics();

  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const timelineProgress = useTransform(scrollYProgress, [0, 1], [0, 100]);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate timeline items on scroll
      projectMilestones.forEach((_, index) => {
        const item = document.querySelector(`[data-timeline-item="${index}"]`);
        if (item) {
          gsap.fromTo(item,
            {
              opacity: 0,
              x: index % 2 === 0 ? -100 : 100,
              scale: 0.8
            },
            {
              opacity: 1,
              x: 0,
              scale: 1,
              duration: 1,
              ease: "power3.out",
              scrollTrigger: {
                trigger: item,
                start: "top 80%",
                end: "bottom 20%",
                toggleActions: "play none none reverse",
                onEnter: () => setActiveIndex(index),
                onEnterBack: () => setActiveIndex(index)
              }
            }
          );
        }
      });

      // Animate progress line
      gsap.fromTo(timelineRef.current,
        { scaleY: 0 },
        {
          scaleY: 1,
          duration: 2,
          ease: "power2.out",
          scrollTrigger: {
            trigger: containerRef.current,
            start: "top 80%",
            end: "bottom 20%",
            scrub: 1
          }
        }
      );
    }, containerRef);

    return () => ctx.revert();
  }, []);

  const handleProjectClick = (project: ProjectMilestone) => {
    trackEvent('project_timeline_click', {
      project_id: project.id,
      project_title: project.title
    });
  };

  return (
    <section className="py-20 bg-muted/10 overflow-hidden">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Project Timeline
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            A visual journey through my most impactful projects and their real-world results
          </p>
        </motion.div>

        <div ref={containerRef} className="relative max-w-6xl mx-auto">
          {/* Progress indicator */}
          <motion.div
            className="fixed top-1/2 right-8 transform -translate-y-1/2 w-2 h-32 bg-muted rounded-full overflow-hidden z-10"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
          >
            <motion.div
              className="w-full bg-primary rounded-full origin-top"
              style={{ scaleY: timelineProgress.get() / 100 }}
            />
          </motion.div>

          {/* Timeline line */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-border">
            <motion.div
              ref={timelineRef}
              className="w-full bg-primary origin-top"
              style={{ height: '100%' }}
            />
          </div>

          {/* Timeline items */}
          <div className="space-y-24">
            {projectMilestones.map((project, index) => (
              <motion.div
                key={project.id}
                data-timeline-item={index}
                className={`flex items-center ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
                onClick={() => handleProjectClick(project)}
              >
                <div className="w-1/2 px-8">
                  <Card className={`relative cursor-pointer transition-all duration-300 hover:shadow-portfolio ${
                    activeIndex === index ? 'ring-2 ring-primary shadow-portfolio' : ''
                  }`}>
                    <CardContent className="p-6">
                      {/* Date badge */}
                      <div className="flex items-center gap-2 mb-4">
                        <Badge variant="secondary" className="text-xs">
                          <Calendar className="w-3 h-3 mr-1" />
                          {project.date}
                        </Badge>
                        {project.featured && (
                          <Badge className="text-xs bg-primary">
                            <Award className="w-3 h-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>

                      {/* Project info */}
                      <h3 className="text-xl font-bold text-foreground mb-2">
                        {project.title}
                      </h3>
                      <p className="text-primary font-medium mb-3">{project.company}</p>
                      <p className="text-muted-foreground text-sm mb-4 line-clamp-3">
                        {project.description}
                      </p>

                      {/* Metrics */}
                      {project.metrics && (
                        <div className="grid grid-cols-2 gap-2 mb-4">
                          {project.metrics.users && (
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <Users className="w-4 h-4 mx-auto mb-1 text-primary" />
                              <div className="text-xs font-medium">{project.metrics.users}</div>
                              <div className="text-xs text-muted-foreground">Users</div>
                            </div>
                          )}
                          {project.metrics.revenue && (
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <TrendingUp className="w-4 h-4 mx-auto mb-1 text-primary" />
                              <div className="text-xs font-medium">{project.metrics.revenue}</div>
                              <div className="text-xs text-muted-foreground">Revenue</div>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Technologies */}
                      <div className="flex flex-wrap gap-1 mb-4">
                        {project.technologies.slice(0, 4).map((tech) => (
                          <Badge key={tech} variant="outline" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                        {project.technologies.length > 4 && (
                          <Badge variant="outline" className="text-xs">
                            +{project.technologies.length - 4}
                          </Badge>
                        )}
                      </div>

                      {/* Action buttons */}
                      <div className="flex gap-2">
                        {project.liveUrl && (
                          <Button asChild size="sm" className="flex-1">
                            <a href={project.liveUrl} target="_blank" rel="noopener noreferrer">
                              <ExternalLink className="w-3 h-3 mr-1" />
                              Live Demo
                            </a>
                          </Button>
                        )}
                        {project.githubUrl && (
                          <Button asChild variant="outline" size="sm" className="flex-1">
                            <a href={project.githubUrl} target="_blank" rel="noopener noreferrer">
                              <Github className="w-3 h-3 mr-1" />
                              Code
                            </a>
                          </Button>
                        )}
                      </div>
                    </CardContent>

                    {/* Timeline dot */}
                    <div className={`absolute top-8 w-6 h-6 bg-primary rounded-full border-4 border-background shadow-lg ${
                      index % 2 === 0 ? '-right-3' : '-left-3'
                    } ${activeIndex === index ? 'scale-125' : ''} transition-transform duration-300`}>
                      <div className="absolute inset-1 bg-white rounded-full animate-pulse" />
                    </div>
                  </Card>
                </div>

                {/* Project image */}
                <div className="w-1/2 px-8">
                  {project.image && (
                    <motion.div
                      className="relative"
                      whileHover={{ scale: 1.05 }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      <LazyImage
                        src={project.image}
                        alt={`${project.title} preview`}
                        className="w-full h-64 object-cover rounded-lg shadow-lg"
                        containerClassName="w-full h-64 rounded-lg overflow-hidden"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-lg" />
                    </motion.div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProjectTimeline;
