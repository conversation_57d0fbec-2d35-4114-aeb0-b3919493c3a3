import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Star, TrendingUp, Award, Code, Database, Smartphone, Cloud } from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';

gsap.registerPlugin(ScrollTrigger);

interface Skill {
  name: string;
  level: number;
  experience: string;
  projects: number;
  category: 'frontend' | 'backend' | 'mobile' | 'tools' | 'database' | 'cloud';
  icon?: string;
  description: string;
  trending?: boolean;
  certification?: boolean;
}

interface SkillCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  skills: Skill[];
}

const skillsData: SkillCategory[] = [
  {
    id: 'frontend',
    name: 'Frontend',
    icon: <Code className="w-5 h-5" />,
    color: 'from-blue-500 to-cyan-500',
    skills: [
      {
        name: 'React',
        level: 95,
        experience: '4+ years',
        projects: 25,
        category: 'frontend',
        description: 'Advanced React development with hooks, context, and performance optimization',
        trending: true,
        certification: true
      },
      {
        name: 'Next.js',
        level: 90,
        experience: '3+ years',
        projects: 15,
        category: 'frontend',
        description: 'Full-stack React framework with SSR, SSG, and API routes',
        trending: true
      },
      {
        name: 'TypeScript',
        level: 88,
        experience: '3+ years',
        projects: 20,
        category: 'frontend',
        description: 'Type-safe JavaScript development with advanced type systems',
        trending: true
      },
      {
        name: 'Tailwind CSS',
        level: 92,
        experience: '2+ years',
        projects: 18,
        category: 'frontend',
        description: 'Utility-first CSS framework for rapid UI development'
      }
    ]
  },
  {
    id: 'backend',
    name: 'Backend',
    icon: <Database className="w-5 h-5" />,
    color: 'from-green-500 to-emerald-500',
    skills: [
      {
        name: 'Node.js',
        level: 90,
        experience: '4+ years',
        projects: 22,
        category: 'backend',
        description: 'Server-side JavaScript with Express, Fastify, and microservices',
        certification: true
      },
      {
        name: 'PostgreSQL',
        level: 85,
        experience: '3+ years',
        projects: 16,
        category: 'backend',
        description: 'Advanced SQL, query optimization, and database design'
      },
      {
        name: 'Python',
        level: 80,
        experience: '2+ years',
        projects: 12,
        category: 'backend',
        description: 'Backend development with Django, FastAPI, and data processing'
      },
      {
        name: 'GraphQL',
        level: 75,
        experience: '2+ years',
        projects: 8,
        category: 'backend',
        description: 'API development with Apollo Server and schema design',
        trending: true
      }
    ]
  },
  {
    id: 'mobile',
    name: 'Mobile',
    icon: <Smartphone className="w-5 h-5" />,
    color: 'from-purple-500 to-pink-500',
    skills: [
      {
        name: 'React Native',
        level: 82,
        experience: '2+ years',
        projects: 8,
        category: 'mobile',
        description: 'Cross-platform mobile development with native performance'
      },
      {
        name: 'Flutter',
        level: 70,
        experience: '1+ year',
        projects: 4,
        category: 'mobile',
        description: 'Google\'s UI toolkit for cross-platform mobile development',
        trending: true
      }
    ]
  },
  {
    id: 'cloud',
    name: 'Cloud & DevOps',
    icon: <Cloud className="w-5 h-5" />,
    color: 'from-orange-500 to-red-500',
    skills: [
      {
        name: 'AWS',
        level: 85,
        experience: '3+ years',
        projects: 14,
        category: 'cloud',
        description: 'EC2, S3, Lambda, RDS, and serverless architecture',
        certification: true
      },
      {
        name: 'Docker',
        level: 80,
        experience: '2+ years',
        projects: 12,
        category: 'cloud',
        description: 'Containerization and orchestration with Docker Compose'
      },
      {
        name: 'Vercel',
        level: 90,
        experience: '2+ years',
        projects: 16,
        category: 'cloud',
        description: 'Frontend deployment and serverless functions'
      }
    ]
  }
];

const InteractiveSkills = () => {
  const [activeCategory, setActiveCategory] = useState('frontend');
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);
  const [animatedLevels, setAnimatedLevels] = useState<Record<string, number>>({});
  const containerRef = useRef<HTMLDivElement>(null);
  const { trackSkillInteraction } = useAnalytics();

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate skill bars on scroll
      skillsData.forEach((category) => {
        category.skills.forEach((skill) => {
          const skillElement = document.querySelector(`[data-skill="${skill.name}"]`);
          if (skillElement) {
            ScrollTrigger.create({
              trigger: skillElement,
              start: "top 80%",
              onEnter: () => {
                gsap.to({}, {
                  duration: 1.5,
                  ease: "power2.out",
                  onUpdate: function() {
                    const progress = this.progress();
                    setAnimatedLevels(prev => ({
                      ...prev,
                      [skill.name]: Math.round(skill.level * progress)
                    }));
                  }
                });
              }
            });
          }
        });
      });
    }, containerRef);

    return () => ctx.revert();
  }, []);

  const handleSkillClick = (skill: Skill) => {
    setSelectedSkill(skill);
    trackSkillInteraction(skill.name);
  };

  const getSkillColor = (level: number) => {
    if (level >= 90) return 'bg-green-500';
    if (level >= 80) return 'bg-blue-500';
    if (level >= 70) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  const currentCategory = skillsData.find(cat => cat.id === activeCategory);

  return (
    <section ref={containerRef} className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Interactive Skills
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Explore my technical expertise with interactive visualizations and detailed proficiency levels
          </p>
        </motion.div>

        <Tabs value={activeCategory} onValueChange={setActiveCategory} className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-8">
            {skillsData.map((category) => (
              <TabsTrigger
                key={category.id}
                value={category.id}
                className="flex items-center gap-2"
              >
                {category.icon}
                <span className="hidden sm:inline">{category.name}</span>
              </TabsTrigger>
            ))}
          </TabsList>

          {skillsData.map((category) => (
            <TabsContent key={category.id} value={category.id}>
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                className="grid grid-cols-1 md:grid-cols-2 gap-6"
              >
                {category.skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    data-skill={skill.name}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                    className="cursor-pointer"
                    onClick={() => handleSkillClick(skill)}
                  >
                    <Card className="h-full hover:shadow-portfolio transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="text-lg font-semibold text-foreground">
                                {skill.name}
                              </h3>
                              {skill.trending && (
                                <Badge className="text-xs bg-orange-500">
                                  <TrendingUp className="w-3 h-3 mr-1" />
                                  Trending
                                </Badge>
                              )}
                              {skill.certification && (
                                <Badge className="text-xs bg-blue-500">
                                  <Award className="w-3 h-3 mr-1" />
                                  Certified
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-muted-foreground mb-3">
                              {skill.description}
                            </p>
                          </div>
                        </div>

                        {/* Skill level visualization */}
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium">Proficiency</span>
                            <span className="text-sm font-bold text-primary">
                              {animatedLevels[skill.name] || 0}%
                            </span>
                          </div>
                          
                          <div className="relative">
                            <Progress 
                              value={animatedLevels[skill.name] || 0} 
                              className="h-2"
                            />
                            <motion.div
                              className={`absolute top-0 left-0 h-2 rounded-full ${getSkillColor(skill.level)}`}
                              initial={{ width: 0 }}
                              animate={{ width: `${animatedLevels[skill.name] || 0}%` }}
                              transition={{ duration: 1.5, ease: "easeOut" }}
                            />
                          </div>

                          {/* Skill metrics */}
                          <div className="grid grid-cols-2 gap-4 mt-4">
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-sm font-medium text-foreground">
                                {skill.experience}
                              </div>
                              <div className="text-xs text-muted-foreground">Experience</div>
                            </div>
                            <div className="text-center p-2 bg-muted/50 rounded">
                              <div className="text-sm font-medium text-foreground">
                                {skill.projects}+
                              </div>
                              <div className="text-xs text-muted-foreground">Projects</div>
                            </div>
                          </div>

                          {/* Star rating */}
                          <div className="flex items-center gap-1 mt-3">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < Math.floor(skill.level / 20)
                                    ? 'text-yellow-500 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                            <span className="text-xs text-muted-foreground ml-2">
                              {Math.floor(skill.level / 20)}/5
                            </span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </motion.div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Skill detail modal */}
        <AnimatePresence>
          {selectedSkill && (
            <motion.div
              className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setSelectedSkill(null)}
            >
              <motion.div
                className="bg-background rounded-lg p-6 max-w-md w-full"
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.8, opacity: 0 }}
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-xl font-bold text-foreground">
                    {selectedSkill.name}
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSelectedSkill(null)}
                  >
                    ✕
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <p className="text-muted-foreground">
                    {selectedSkill.description}
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-muted/50 rounded">
                      <div className="text-lg font-bold text-primary">
                        {selectedSkill.level}%
                      </div>
                      <div className="text-xs text-muted-foreground">Proficiency</div>
                    </div>
                    <div className="text-center p-3 bg-muted/50 rounded">
                      <div className="text-lg font-bold text-primary">
                        {selectedSkill.projects}+
                      </div>
                      <div className="text-xs text-muted-foreground">Projects</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-center gap-1">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`w-5 h-5 ${
                          i < Math.floor(selectedSkill.level / 20)
                            ? 'text-yellow-500 fill-current'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                  
                  <div className="flex flex-wrap gap-2 justify-center">
                    {selectedSkill.trending && (
                      <Badge className="bg-orange-500">
                        <TrendingUp className="w-3 h-3 mr-1" />
                        Trending
                      </Badge>
                    )}
                    {selectedSkill.certification && (
                      <Badge className="bg-blue-500">
                        <Award className="w-3 h-3 mr-1" />
                        Certified
                      </Badge>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Skills summary */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <Card className="max-w-4xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-bold text-foreground mb-6">
                Skills Overview
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {skillsData.map((category) => (
                  <div key={category.id} className="text-center">
                    <div className={`w-16 h-16 mx-auto mb-3 rounded-full bg-gradient-to-r ${category.color} flex items-center justify-center text-white`}>
                      {category.icon}
                    </div>
                    <h4 className="font-semibold text-foreground mb-1">
                      {category.name}
                    </h4>
                    <p className="text-sm text-muted-foreground">
                      {category.skills.length} skills
                    </p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};

export default InteractiveSkills;
