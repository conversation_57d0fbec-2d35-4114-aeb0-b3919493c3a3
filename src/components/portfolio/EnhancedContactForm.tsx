import React, { useState, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { AnimatedInput } from '@/components/ui/animated-input';
import { AnimatedTextarea } from '@/components/ui/animated-textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  CheckCircle, 
  AlertCircle, 
  Send, 
  Clock, 
  User, 
  Mail, 
  MessageSquare,
  Sparkles,
  Heart,
  Zap,
  ArrowRight,
  ArrowLeft
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useAnalytics } from '@/hooks/use-analytics';

const contactSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  subject: z.string().min(5, 'Subject must be at least 5 characters'),
  message: z.string().min(20, 'Message must be at least 20 characters'),
  budget: z.string().optional(),
  timeline: z.string().optional(),
  projectType: z.string().optional()
});

type ContactForm = z.infer<typeof contactSchema>;

interface FormStep {
  id: string;
  title: string;
  description: string;
  fields: (keyof ContactForm)[];
  icon: React.ReactNode;
}

const formSteps: FormStep[] = [
  {
    id: 'personal',
    title: 'Personal Info',
    description: 'Tell me about yourself',
    fields: ['name', 'email'],
    icon: <User className="w-5 h-5" />
  },
  {
    id: 'project',
    title: 'Project Details',
    description: 'What can I help you with?',
    fields: ['subject', 'projectType', 'budget', 'timeline'],
    icon: <Zap className="w-5 h-5" />
  },
  {
    id: 'message',
    title: 'Your Message',
    description: 'Share your vision',
    fields: ['message'],
    icon: <MessageSquare className="w-5 h-5" />
  }
];

const projectTypes = [
  'Web Application',
  'Mobile App',
  'E-commerce',
  'Portfolio/Landing Page',
  'API Development',
  'Consulting',
  'Other'
];

const budgetRanges = [
  'Under $5K',
  '$5K - $15K',
  '$15K - $30K',
  '$30K - $50K',
  '$50K+',
  'Let\'s discuss'
];

const timelineOptions = [
  'ASAP',
  '1-2 weeks',
  '1 month',
  '2-3 months',
  '3+ months',
  'Flexible'
];

const EnhancedContactForm = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [formProgress, setFormProgress] = useState(0);
  const formRef = useRef<HTMLFormElement>(null);
  const { toast } = useToast();
  const { trackContactFormSubmit, trackEvent } = useAnalytics();

  const form = useForm<ContactForm>({
    resolver: zodResolver(contactSchema),
    mode: 'onChange'
  });

  const watchedFields = form.watch();

  // Calculate form progress
  const calculateProgress = () => {
    const totalFields = Object.keys(contactSchema.shape).length;
    const filledFields = Object.values(watchedFields).filter(value => 
      value && value.toString().trim() !== ''
    ).length;
    return Math.round((filledFields / totalFields) * 100);
  };

  // Update progress when form changes
  React.useEffect(() => {
    setFormProgress(calculateProgress());
  }, [watchedFields]);

  const onSubmit = async (data: ContactForm) => {
    setIsSubmitting(true);
    
    try {
      // Track form submission
      trackContactFormSubmit();
      trackEvent('contact_form_completed', {
        project_type: data.projectType || 'not_specified',
        budget_range: data.budget || 'not_specified',
        timeline: data.timeline || 'not_specified'
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsSuccess(true);
      
      toast({
        title: "Message sent successfully! 🎉",
        description: "Thanks for reaching out. I'll reply within 24 hours.",
      });

      // Reset form after success
      setTimeout(() => {
        form.reset();
        setCurrentStep(0);
        setIsSuccess(false);
        setFormProgress(0);
      }, 3000);

    } catch (error) {
      toast({
        title: "Something went wrong",
        description: "Please try again or contact me directly.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    const currentFields = formSteps[currentStep].fields;
    const isStepValid = currentFields.every(field => {
      const value = form.getValues(field);
      return value && value.toString().trim() !== '';
    });

    if (isStepValid) {
      setCurrentStep(prev => Math.min(prev + 1, formSteps.length - 1));
      trackEvent('contact_form_step_completed', { step: currentStep + 1 });
    } else {
      // Trigger validation for current step fields
      currentFields.forEach(field => form.trigger(field));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0));
  };

  const currentStepData = formSteps[currentStep];
  const isLastStep = currentStep === formSteps.length - 1;
  const canProceed = currentStepData.fields.every(field => {
    const value = form.getValues(field);
    return value && value.toString().trim() !== '';
  });

  if (isSuccess) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-12"
      >
        <motion.div
          animate={{ 
            scale: [1, 1.2, 1],
            rotate: [0, 10, -10, 0]
          }}
          transition={{ duration: 0.6 }}
          className="w-20 h-20 mx-auto mb-6 bg-green-500 rounded-full flex items-center justify-center"
        >
          <CheckCircle className="w-10 h-10 text-white" />
        </motion.div>
        
        <h3 className="text-2xl font-bold text-foreground mb-4">
          Message Sent Successfully!
        </h3>
        <p className="text-muted-foreground mb-6">
          Thank you for reaching out. I'll get back to you within 24 hours.
        </p>
        
        <motion.div
          className="flex items-center justify-center gap-2 text-primary"
          animate={{ opacity: [0.5, 1, 0.5] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          <Heart className="w-5 h-5" />
          <span>Looking forward to working together!</span>
          <Sparkles className="w-5 h-5" />
        </motion.div>
      </motion.div>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto shadow-portfolio bg-card/50 backdrop-blur-sm">
      <CardHeader className="text-center pb-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <CardTitle className="text-3xl font-black text-foreground mb-2 bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
            Let's Work Together
          </CardTitle>
          <p className="text-muted-foreground text-lg">
            Tell me about your project and let's create something amazing
          </p>
        </motion.div>
        
        {/* Progress bar */}
        <motion.div 
          className="mt-8"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-muted-foreground">Completion</span>
            <span className="text-sm font-medium text-primary">{formProgress}%</span>
          </div>
          <Progress value={formProgress} className="h-1.5" />
        </motion.div>

        {/* Step indicators */}
        <div className="flex items-center justify-center gap-2 mt-8">
          {formSteps.map((step, index) => (
            <motion.div
              key={step.id}
              className="flex flex-col items-center"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
            >
              <div 
                className={`
                  relative flex items-center justify-center w-10 h-10 rounded-full 
                  transition-all duration-300 ease-in-out
                  ${index === currentStep 
                    ? 'bg-primary text-primary-foreground ring-2 ring-primary ring-offset-2' 
                    : index < currentStep
                    ? 'bg-green-500 text-white'
                    : 'bg-muted text-muted-foreground'
                  }
                `}
              >
                {index < currentStep ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  step.icon
                )}
                {index < formSteps.length - 1 && (
                  <div className={`absolute left-full w-8 h-0.5 -translate-y-1/2 top-1/2 
                    ${index < currentStep ? 'bg-green-500' : 'bg-muted'}`} 
                  />
                )}
              </div>
              <span className={`mt-2 text-xs font-medium transition-colors duration-300
                ${index === currentStep ? 'text-primary' : 'text-muted-foreground'}`}>
                {step.title}
              </span>
            </motion.div>
          ))}
        </div>
      </CardHeader>

      <CardContent className="px-6 py-8">
        <form ref={formRef} onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className="space-y-8"
            >
              <div className="text-center mb-8">
                <h3 className="text-xl font-bold text-foreground mb-2">
                  {currentStepData.title}
                </h3>
                <p className="text-muted-foreground">
                  {currentStepData.description}
                </p>
              </div>

              {/* Step 1: Personal Info */}
              {currentStep === 0 && (
                <div className="space-y-6">
                  <AnimatedInput
                    label="Full Name"
                    {...form.register('name')}
                    placeholder="John Doe"
                    error={form.formState.errors.name?.message}
                  />
                  <AnimatedInput
                    label="Email Address"
                    type="email"
                    {...form.register('email')}
                    placeholder="<EMAIL>"
                    error={form.formState.errors.email?.message}
                  />
                </div>
              )}

              {/* Step 2: Project Details */}
              {currentStep === 1 && (
                <div className="space-y-6">
                  <AnimatedInput
                    label="Project Subject"
                    {...form.register('subject')}
                    placeholder="Web application development"
                    error={form.formState.errors.subject?.message}
                  />
                  
                  <div>
                    <label className="block text-sm font-medium text-foreground mb-4">
                      Project Type
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {projectTypes.map((type) => (
                        <motion.button
                          key={type}
                          type="button"
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                          className={`p-3 text-sm rounded-xl border transition-all duration-300 ${
                            form.watch('projectType') === type
                              ? 'bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/20'
                              : 'bg-card border-border hover:bg-accent hover:border-accent'
                          }`}
                          onClick={() => form.setValue('projectType', type)}
                        >
                          {type}
                        </motion.button>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-4">
                        Budget Range
                      </label>
                      <div className="space-y-3">
                        {budgetRanges.map((budget) => (
                          <motion.button
                            key={budget}
                            type="button"
                            whileHover={{ scale: 1.02 }}
                            className={`w-full p-3 text-sm rounded-xl border transition-all duration-300 ${
                              form.watch('budget') === budget
                                ? 'bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/20'
                                : 'bg-card border-border hover:bg-accent hover:border-accent'
                            }`}
                            onClick={() => form.setValue('budget', budget)}
                          >
                            {budget}
                          </motion.button>
                        ))}
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-4">
                        Timeline
                      </label>
                      <div className="space-y-3">
                        {timelineOptions.map((timeline) => (
                          <motion.button
                            key={timeline}
                            type="button"
                            whileHover={{ scale: 1.02 }}
                            className={`w-full p-3 text-sm rounded-xl border transition-all duration-300 ${
                              form.watch('timeline') === timeline
                                ? 'bg-primary text-primary-foreground border-primary shadow-lg shadow-primary/20'
                                : 'bg-card border-border hover:bg-accent hover:border-accent'
                            }`}
                            onClick={() => form.setValue('timeline', timeline)}
                          >
                            {timeline}
                          </motion.button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Step 3: Message */}
              {currentStep === 2 && (
                <div className="space-y-6">
                  <AnimatedTextarea
                    label="Project Details"
                    {...form.register('message')}
                    placeholder="Tell me about your project, goals, and any specific requirements..."
                    rows={6}
                    error={form.formState.errors.message?.message}
                  />
                  
                  <motion.div 
                    className="bg-muted/30 p-6 rounded-xl border border-border/50"
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <h4 className="font-semibold text-foreground mb-4 flex items-center gap-2">
                      <Clock className="w-4 h-4 text-primary" />
                      What happens next?
                    </h4>
                    <ul className="text-sm text-muted-foreground space-y-3">
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                        I'll review your project details within 24 hours
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                        We'll schedule a call to discuss requirements
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                        You'll receive a detailed proposal and timeline
                      </li>
                      <li className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                        We'll start building your amazing project
                      </li>
                    </ul>
                  </motion.div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          {/* Navigation buttons */}
          <div className="flex items-center justify-between pt-8">
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="flex items-center gap-2 px-6"
            >
              <ArrowLeft className="w-4 h-4" />
              Back
            </Button>

            {isLastStep ? (
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  type="submit"
                  disabled={isSubmitting || !canProceed}
                  className="flex items-center gap-2 px-6 min-w-[140px]"
                >
                  {isSubmitting ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      >
                        <Clock className="w-4 h-4" />
                      </motion.div>
                      Sending...
                    </>
                  ) : (
                    <>
                      Send Message
                      <Send className="w-4 h-4" />
                    </>
                  )}
                </Button>
              </motion.div>
            ) : (
              <Button
                type="button"
                onClick={nextStep}
                disabled={!canProceed}
                className="flex items-center gap-2 px-6"
              >
                Continue
                <ArrowRight className="w-4 h-4" />
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default EnhancedContactForm;
