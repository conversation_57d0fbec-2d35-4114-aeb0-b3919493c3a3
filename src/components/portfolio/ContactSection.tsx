import { motion } from 'framer-motion';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Mail, Linkedin, Twitter, Download, Clock, MapPin, Phone, Instagram, Facebook } from 'lucide-react';
import { useAnalytics } from '@/hooks/use-analytics';
import EnhancedContactForm from './EnhancedContactForm';

const ContactSection = () => {
  const { trackResumeDownload, trackSocialClick } = useAnalytics();

  return (
    <section id="contact" className="py-20 bg-background">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Let's Work Together
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Have a project in mind? Let's discuss how we can turn your ideas into reality.
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* Enhanced Contact Form */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            <EnhancedContactForm />
          </motion.div>

          {/* Contact Info */}
          <motion.div
            className="space-y-8"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <motion.div
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card>
              <CardHeader>
                <CardTitle className="text-xl">Other Ways to Connect</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <motion.a
                  href="mailto:<EMAIL>"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  onClick={() => trackSocialClick('email')}
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Mail className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div>
                    <div className="font-medium">Email</div>
                    <div className="text-sm text-muted-foreground"><EMAIL></div>
                  </div>
                </motion.a>

                <motion.a
                  href="https://linkedin.com/in/rainernsa"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  onClick={() => trackSocialClick('linkedin')}
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Linkedin className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div>
                    <div className="font-medium">LinkedIn</div>
                    <div className="text-sm text-muted-foreground">Professional updates</div>
                  </div>
                </motion.a>

                <motion.a
                  href="https://x.com/Woldreamz"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  onClick={() => trackSocialClick('twitter')}
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Twitter className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div>
                    <div className="font-medium">X (Twitter)</div>
                    <div className="text-sm text-muted-foreground">Tech thoughts & updates</div>
                  </div>
                </motion.a>

                <motion.a
                  href="https://www.instagram.com/woldreamz/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  onClick={() => trackSocialClick('instagram')}
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Instagram className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div>
                    <div className="font-medium">Instagram</div>
                    <div className="text-sm text-muted-foreground">Behind the scenes</div>
                  </div>
                </motion.a>

                <motion.a
                  href="https://www.facebook.com/reyonsa/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-3 p-3 rounded-lg hover:bg-muted transition-colors group"
                  whileHover={{ x: 5 }}
                  transition={{ type: "spring", stiffness: 300 }}
                  onClick={() => trackSocialClick('facebook')}
                >
                  <motion.div
                    whileHover={{ rotate: 10, scale: 1.1 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Facebook className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div>
                    <div className="font-medium">Facebook</div>
                    <div className="text-sm text-muted-foreground">Personal updates</div>
                  </div>
                </motion.a>
              </CardContent>
            </Card>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="bg-primary text-primary-foreground">
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Download My Resume</h3>
                  <p className="text-primary-foreground/80 mb-4 text-sm">
                    Get a detailed overview of my experience, projects, and technical skills.
                  </p>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Button
                      variant="secondary"
                      className="w-full bg-white text-primary hover:bg-white/90"
                      asChild
                    >
                      <a
                        href="/resume.pdf"
                        download="Rayner_Resume.pdf"
                        onClick={trackResumeDownload}
                      >
                        <motion.div
                          animate={{ y: [0, -2, 0] }}
                          transition={{ duration: 2, repeat: Infinity }}
                        >
                          <Download className="h-4 w-4 mr-2" />
                        </motion.div>
                        Download Resume
                      </a>
                    </Button>
                  </motion.div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              whileHover={{ y: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <Card className="bg-muted/50">
                <CardContent className="p-6">
                  <h3 className="font-semibold mb-4">Let's Build Something Great</h3>
                  <ul className="space-y-2 text-sm text-muted-foreground">
                    <motion.li
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}
                    >
                      • Full-stack web applications
                    </motion.li>
                    <motion.li
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                    >
                      • Mobile app development
                    </motion.li>
                    <motion.li
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.3 }}
                    >
                      • API design & development
                    </motion.li>
                    <motion.li
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.4 }}
                    >
                      • Technical consulting
                    </motion.li>
                    <motion.li
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.5 }}
                    >
                      • Team mentoring
                    </motion.li>
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;