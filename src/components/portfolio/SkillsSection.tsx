import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

const skills = {
  frontend: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS'],
  backend: ['Node.js', 'Express', 'PostgreSQL', 'Python'],
  mobile: ['React Native'],
  tools: ['Git', 'Docker', 'AWS', 'Vercel']
};

const SkillsSection = () => {
  return (
    <section className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Skills & Tech
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Modern technologies I use to build scalable, performant applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">⚛️</div>
              <h3 className="font-semibold mb-4">Frontend</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.frontend.map((skill) => (
                  <Badge key={skill} variant="secondary">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">🛠️</div>
              <h3 className="font-semibold mb-4">Backend</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.backend.map((skill) => (
                  <Badge key={skill} variant="secondary">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="font-semibold mb-4">Mobile</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.mobile.map((skill) => (
                  <Badge key={skill} variant="secondary">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">🔧</div>
              <h3 className="font-semibold mb-4">Tools</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.tools.map((skill) => (
                  <Badge key={skill} variant="secondary">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Testimonial */}
        <Card className="bg-primary text-primary-foreground max-w-4xl mx-auto">
          <CardContent className="p-8 text-center">
            <blockquote className="text-xl lg:text-2xl font-medium mb-4 italic">
              "Rainer is that rare developer who combines technical prowess with a business-first mindset."
            </blockquote>
            <cite className="text-primary-foreground/80">Product Manager, StartCo</cite>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default SkillsSection;