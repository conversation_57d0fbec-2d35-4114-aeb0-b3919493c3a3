import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

gsap.registerPlugin(ScrollTrigger);

const skills = {
  frontend: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS'],
  backend: ['Node.js', 'Express', 'PostgreSQL', 'Python'],
  mobile: ['React Native'],
  tools: ['Git', 'Docker', 'AWS', 'Vercel']
};

const SkillsSection = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const skillsGridRef = useRef<HTMLDivElement>(null);
  const testimonialRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate header
      gsap.fromTo(headerRef.current?.children || [],
        {
          opacity: 0,
          y: 30
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: headerRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate skill cards with stagger
      gsap.fromTo(skillsGridRef.current?.children || [],
        {
          opacity: 0,
          y: 50,
          scale: 0.9
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.6,
          stagger: 0.15,
          ease: "back.out(1.7)",
          scrollTrigger: {
            trigger: skillsGridRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

      // Animate badges within each card
      const skillCards = skillsGridRef.current?.children;
      if (skillCards) {
        Array.from(skillCards).forEach((card, index) => {
          const badges = card.querySelectorAll('.skill-badge');
          gsap.fromTo(badges,
            {
              opacity: 0,
              scale: 0.8
            },
            {
              opacity: 1,
              scale: 1,
              duration: 0.4,
              stagger: 0.1,
              ease: "back.out(1.7)",
              delay: index * 0.15 + 0.3,
              scrollTrigger: {
                trigger: card,
                start: "top 85%",
                toggleActions: "play none none reverse"
              }
            }
          );
        });
      }

      // Animate testimonial
      gsap.fromTo(testimonialRef.current,
        {
          opacity: 0,
          y: 30,
          scale: 0.95
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 0.8,
          ease: "power3.out",
          scrollTrigger: {
            trigger: testimonialRef.current,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );

    }, sectionRef);

    return () => ctx.revert();
  }, []);

  return (
    <section ref={sectionRef} className="py-20 bg-muted/30">
      <div className="container mx-auto px-4">
        <div ref={headerRef} className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Skills & Tech
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Modern technologies I use to build scalable, performant applications
          </p>
        </div>

        <div ref={skillsGridRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">⚛️</div>
              <h3 className="font-semibold mb-4">Frontend</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.frontend.map((skill) => (
                  <Badge key={skill} variant="secondary" className="skill-badge">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">🛠️</div>
              <h3 className="font-semibold mb-4">Backend</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.backend.map((skill) => (
                  <Badge key={skill} variant="secondary" className="skill-badge">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">📱</div>
              <h3 className="font-semibold mb-4">Mobile</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.mobile.map((skill) => (
                  <Badge key={skill} variant="secondary" className="skill-badge">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="text-center">
            <CardContent className="pt-6">
              <div className="text-4xl mb-4">🔧</div>
              <h3 className="font-semibold mb-4">Tools</h3>
              <div className="flex flex-wrap gap-2 justify-center">
                {skills.tools.map((skill) => (
                  <Badge key={skill} variant="secondary" className="skill-badge">{skill}</Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Testimonial */}
        <Card ref={testimonialRef} className="bg-primary text-primary-foreground max-w-4xl mx-auto">
          <CardContent className="p-8 text-center">
            <blockquote className="text-xl lg:text-2xl font-medium mb-4 italic">
              "Rainer is that rare developer who combines technical prowess with a business-first mindset."
            </blockquote>
            <cite className="text-primary-foreground/80">Product Manager, StartCo</cite>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default SkillsSection;