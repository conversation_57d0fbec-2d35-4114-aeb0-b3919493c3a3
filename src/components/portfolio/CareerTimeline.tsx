import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

gsap.registerPlugin(ScrollTrigger);

interface TimelineItem {
  year: string;
  title: string;
  company: string;
  description: string;
  achievements: string[];
  technologies: string[];
}

const timelineData: TimelineItem[] = [
  {
    year: "2024",
    title: "Senior Full-Stack Developer",
    company: "Freelance & Consulting",
    description: "Leading development of high-impact applications across healthcare, real estate, and fintech sectors.",
    achievements: [
      "Built PHCityRent platform with 150% user growth",
      "Developed MedEquip system reducing costs by 50%",
      "Mentored 15+ junior developers"
    ],
    technologies: ["React", "Next.js", "Node.js", "PostgreSQL", "AWS"]
  },
  {
    year: "2023",
    title: "Full-Stack Developer",
    company: "Health Tech Startup",
    description: "Architected and developed Infinity Health Suite, transforming healthcare data management.",
    achievements: [
      "Reduced reporting time by 58%",
      "Improved data accuracy by 40%",
      "Deployed across 15+ clinics"
    ],
    technologies: ["Next.js", "TypeScript", "PostgreSQL", "D3.js", "Vercel"]
  },
  {
    year: "2022",
    title: "Frontend Developer",
    company: "Tech Solutions Inc",
    description: "Specialized in creating responsive, user-friendly interfaces for enterprise applications.",
    achievements: [
      "Improved user engagement by 35%",
      "Reduced page load times by 60%",
      "Led UI/UX redesign initiatives"
    ],
    technologies: ["React", "JavaScript", "CSS3", "Figma"]
  },
  {
    year: "2021",
    title: "Junior Developer",
    company: "StartCo",
    description: "Started my professional journey building web applications and learning industry best practices.",
    achievements: [
      "Delivered 20+ projects on time",
      "Learned modern development workflows",
      "Contributed to team productivity improvements"
    ],
    technologies: ["HTML", "CSS", "JavaScript", "React"]
  }
];

const CareerTimeline = () => {
  const timelineRef = useRef<HTMLDivElement>(null);
  const lineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Animate the timeline line
      gsap.fromTo(lineRef.current,
        {
          scaleY: 0,
          transformOrigin: "top"
        },
        {
          scaleY: 1,
          duration: 2,
          ease: "power2.out",
          scrollTrigger: {
            trigger: timelineRef.current,
            start: "top 80%",
            end: "bottom 20%",
            scrub: 1
          }
        }
      );

      // Animate timeline items
      const items = timelineRef.current?.querySelectorAll('.timeline-item');
      items?.forEach((item, index) => {
        gsap.fromTo(item,
          {
            opacity: 0,
            x: index % 2 === 0 ? -50 : 50,
            scale: 0.9
          },
          {
            opacity: 1,
            x: 0,
            scale: 1,
            duration: 0.8,
            ease: "power3.out",
            scrollTrigger: {
              trigger: item,
              start: "top 85%",
              toggleActions: "play none none reverse"
            }
          }
        );

        // Animate badges within each item
        const badges = item.querySelectorAll('.tech-badge');
        gsap.fromTo(badges,
          {
            opacity: 0,
            scale: 0.8
          },
          {
            opacity: 1,
            scale: 1,
            duration: 0.4,
            stagger: 0.1,
            ease: "back.out(1.7)",
            delay: 0.3,
            scrollTrigger: {
              trigger: item,
              start: "top 80%",
              toggleActions: "play none none reverse"
            }
          }
        );
      });

    }, timelineRef);

    return () => ctx.revert();
  }, []);

  return (
    <section className="py-20 bg-muted/20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-4">
            Career Journey
          </h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            My professional growth and key milestones in software development
          </p>
        </div>

        <div ref={timelineRef} className="relative max-w-6xl mx-auto">
          {/* Timeline Line */}
          <div 
            ref={lineRef}
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-primary/30 h-full"
          />

          {/* Timeline Items */}
          <div className="space-y-12">
            {timelineData.map((item, index) => (
              <div
                key={item.year}
                className={`timeline-item flex items-center ${
                  index % 2 === 0 ? 'flex-row' : 'flex-row-reverse'
                }`}
              >
                <div className="w-1/2 px-8">
                  <Card className="relative">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="text-2xl font-bold text-primary">{item.year}</div>
                        <div className="h-px bg-border flex-1" />
                      </div>
                      
                      <h3 className="text-xl font-semibold text-foreground mb-2">
                        {item.title}
                      </h3>
                      <p className="text-primary font-medium mb-3">{item.company}</p>
                      <p className="text-muted-foreground mb-4">{item.description}</p>
                      
                      <div className="space-y-3">
                        <div>
                          <h4 className="font-medium text-foreground mb-2">Key Achievements:</h4>
                          <ul className="space-y-1">
                            {item.achievements.map((achievement, i) => (
                              <li key={i} className="text-sm text-muted-foreground flex items-start gap-2">
                                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                                {achievement}
                              </li>
                            ))}
                          </ul>
                        </div>
                        
                        <div>
                          <h4 className="font-medium text-foreground mb-2">Technologies:</h4>
                          <div className="flex flex-wrap gap-2">
                            {item.technologies.map((tech) => (
                              <Badge key={tech} variant="secondary" className="tech-badge text-xs">
                                {tech}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </CardContent>
                    
                    {/* Timeline dot */}
                    <div className={`absolute top-8 w-4 h-4 bg-primary rounded-full border-4 border-background ${
                      index % 2 === 0 ? '-right-2' : '-left-2'
                    }`} />
                  </Card>
                </div>
                
                <div className="w-1/2" />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default CareerTimeline;
