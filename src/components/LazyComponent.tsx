import React, { Suspense, ComponentType, ReactNode } from 'react';
import { motion } from 'framer-motion';

interface LazyComponentProps {
  children: ReactNode;
  fallback?: ReactNode;
  errorFallback?: ReactNode;
}

// Default loading fallback
const DefaultLoadingFallback = () => (
  <motion.div
    className="flex items-center justify-center p-8"
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    transition={{ duration: 0.3 }}
  >
    <div className="flex items-center space-x-2">
      <div className="w-4 h-4 bg-primary rounded-full animate-pulse"></div>
      <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.1s' }}></div>
      <div className="w-4 h-4 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
    </div>
  </motion.div>
);

// Default error fallback
const DefaultErrorFallback = ({ error, retry }: { error: Error; retry: () => void }) => (
  <motion.div
    className="flex flex-col items-center justify-center p-8 text-center"
    initial={{ opacity: 0, y: 20 }}
    animate={{ opacity: 1, y: 0 }}
    transition={{ duration: 0.3 }}
  >
    <div className="text-4xl mb-4">⚠️</div>
    <h3 className="text-lg font-semibold mb-2">Something went wrong</h3>
    <p className="text-muted-foreground mb-4 text-sm">
      {error.message || 'Failed to load component'}
    </p>
    <button
      onClick={retry}
      className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
    >
      Try Again
    </button>
  </motion.div>
);

// Error boundary component
class LazyErrorBoundary extends React.Component<
  { children: ReactNode; fallback?: ReactNode; onError?: (error: Error) => void },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('LazyComponent Error:', error, errorInfo);
    this.props.onError?.(error);
  }

  retry = () => {
    this.setState({ hasError: false, error: null });
  };

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <DefaultErrorFallback 
          error={this.state.error!} 
          retry={this.retry} 
        />
      );
    }

    return this.props.children;
  }
}

// Main LazyComponent wrapper
const LazyComponent = ({ 
  children, 
  fallback = <DefaultLoadingFallback />,
  errorFallback 
}: LazyComponentProps) => {
  return (
    <LazyErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </LazyErrorBoundary>
  );
};

export default LazyComponent;

// Higher-order component for lazy loading
export const withLazyLoading = <P extends object>(
  Component: ComponentType<P>,
  loadingFallback?: ReactNode,
  errorFallback?: ReactNode
) => {
  return (props: P) => (
    <LazyComponent fallback={loadingFallback} errorFallback={errorFallback}>
      <Component {...props} />
    </LazyComponent>
  );
};

// Utility for creating lazy-loaded components with custom loading states
export const createLazyComponent = <P extends object>(
  importFn: () => Promise<{ default: ComponentType<P> }>,
  options?: {
    loadingFallback?: ReactNode;
    errorFallback?: ReactNode;
    preload?: boolean;
  }
) => {
  const LazyLoadedComponent = React.lazy(importFn);
  
  // Preload the component if requested
  if (options?.preload) {
    importFn();
  }

  return (props: P) => (
    <LazyComponent 
      fallback={options?.loadingFallback} 
      errorFallback={options?.errorFallback}
    >
      <LazyLoadedComponent {...props} />
    </LazyComponent>
  );
};

// Skeleton loading components for different content types
export const SkeletonCard = () => (
  <div className="animate-pulse">
    <div className="bg-muted rounded-lg p-6 space-y-4">
      <div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
      <div className="space-y-2">
        <div className="h-3 bg-muted-foreground/20 rounded"></div>
        <div className="h-3 bg-muted-foreground/20 rounded w-5/6"></div>
      </div>
      <div className="flex space-x-2">
        <div className="h-6 bg-muted-foreground/20 rounded w-16"></div>
        <div className="h-6 bg-muted-foreground/20 rounded w-20"></div>
      </div>
    </div>
  </div>
);

export const SkeletonText = ({ lines = 3 }: { lines?: number }) => (
  <div className="animate-pulse space-y-2">
    {Array.from({ length: lines }).map((_, i) => (
      <div 
        key={i}
        className="h-4 bg-muted-foreground/20 rounded"
        style={{ width: `${Math.random() * 40 + 60}%` }}
      ></div>
    ))}
  </div>
);

export const SkeletonImage = ({ className }: { className?: string }) => (
  <div className={`animate-pulse bg-muted-foreground/20 rounded ${className}`}>
    <div className="flex items-center justify-center h-full">
      <svg
        className="w-8 h-8 text-muted-foreground/40"
        fill="currentColor"
        viewBox="0 0 20 20"
      >
        <path
          fillRule="evenodd"
          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
          clipRule="evenodd"
        />
      </svg>
    </div>
  </div>
);
