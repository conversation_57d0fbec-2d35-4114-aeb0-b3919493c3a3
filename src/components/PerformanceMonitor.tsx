import { useEffect } from 'react';
import analytics from '@/lib/analytics';

interface PerformanceMetrics {
  fcp?: number; // First Contentful Paint
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  ttfb?: number; // Time to First Byte
}

const PerformanceMonitor = () => {
  useEffect(() => {
    // Only run in production
    if (process.env.NODE_ENV !== 'production') return;

    const metrics: PerformanceMetrics = {};

    // Measure Core Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint (FCP)
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0] as PerformanceEntry;
      if (fcpEntry) {
        metrics.fcp = Math.round(fcpEntry.startTime);
      }

      // Time to First Byte (TTFB)
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        metrics.ttfb = Math.round(navigationEntry.responseStart - navigationEntry.requestStart);
      }
    };

    // Largest Contentful Paint (LCP)
    const observeLCP = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            metrics.lcp = Math.round(lastEntry.startTime);
          }
        });

        try {
          observer.observe({ entryTypes: ['largest-contentful-paint'] });
        } catch (e) {
          console.warn('LCP observation not supported');
        }
      }
    };

    // First Input Delay (FID)
    const observeFID = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (entry.processingStart && entry.startTime) {
              metrics.fid = Math.round(entry.processingStart - entry.startTime);
            }
          });
        });

        try {
          observer.observe({ entryTypes: ['first-input'] });
        } catch (e) {
          console.warn('FID observation not supported');
        }
      }
    };

    // Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0;
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          metrics.cls = Math.round(clsValue * 1000) / 1000; // Round to 3 decimal places
        });

        try {
          observer.observe({ entryTypes: ['layout-shift'] });
        } catch (e) {
          console.warn('CLS observation not supported');
        }
      }
    };

    // Resource loading performance
    const measureResourcePerformance = () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      
      const resourceMetrics = {
        totalResources: resources.length,
        slowResources: resources.filter(r => r.duration > 1000).length,
        largeResources: resources.filter(r => r.transferSize > 100000).length,
        avgLoadTime: resources.reduce((sum, r) => sum + r.duration, 0) / resources.length
      };

      // Track slow resources
      if (resourceMetrics.slowResources > 0) {
        analytics.track('slow_resources_detected', {
          count: resourceMetrics.slowResources,
          avg_load_time: Math.round(resourceMetrics.avgLoadTime)
        });
      }
    };

    // Memory usage (if available)
    const measureMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        analytics.track('memory_usage', {
          used_js_heap_size: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
          total_js_heap_size: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
          js_heap_size_limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) // MB
        });
      }
    };

    // Connection information
    const measureConnection = () => {
      if ('connection' in navigator) {
        const connection = (navigator as any).connection;
        analytics.track('connection_info', {
          effective_type: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        });
      }
    };

    // Initialize measurements
    measureWebVitals();
    observeLCP();
    observeFID();
    observeCLS();

    // Measure after page load
    window.addEventListener('load', () => {
      setTimeout(() => {
        measureResourcePerformance();
        measureMemoryUsage();
        measureConnection();

        // Send Core Web Vitals to analytics
        if (Object.keys(metrics).length > 0) {
          analytics.track('core_web_vitals', {
            fcp: metrics.fcp || 0,
            lcp: metrics.lcp || 0,
            fid: metrics.fid || 0,
            cls: metrics.cls || 0,
            ttfb: metrics.ttfb || 0
          });
        }
      }, 1000);
    });

    // Performance budget alerts
    const checkPerformanceBudget = () => {
      const budgets = {
        fcp: 2000, // 2 seconds
        lcp: 2500, // 2.5 seconds
        fid: 100,  // 100ms
        cls: 0.1   // 0.1
      };

      Object.entries(budgets).forEach(([metric, budget]) => {
        const value = metrics[metric as keyof PerformanceMetrics];
        if (value && value > budget) {
          analytics.track('performance_budget_exceeded', {
            metric,
            value,
            budget,
            excess: value - budget
          });
        }
      });
    };

    // Check budgets after measurements
    setTimeout(checkPerformanceBudget, 3000);

    // Cleanup function
    return () => {
      // Performance observers are automatically cleaned up
    };
  }, []);

  // This component doesn't render anything
  return null;
};

export default PerformanceMonitor;

// Utility function to get current performance metrics
export const getCurrentPerformanceMetrics = (): PerformanceMetrics => {
  const metrics: PerformanceMetrics = {};

  // Get navigation timing
  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  if (navigation) {
    metrics.ttfb = Math.round(navigation.responseStart - navigation.requestStart);
  }

  // Get paint timing
  const fcp = performance.getEntriesByName('first-contentful-paint')[0];
  if (fcp) {
    metrics.fcp = Math.round(fcp.startTime);
  }

  return metrics;
};

// Function to manually trigger performance measurement
export const measurePerformance = () => {
  const metrics = getCurrentPerformanceMetrics();
  analytics.track('manual_performance_check', metrics);
  return metrics;
};
