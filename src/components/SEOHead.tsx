import { useEffect } from 'react';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  structuredData?: object;
}

const SEOHead = ({
  title = "Rayner - Full Stack Developer | React, Node.js & Modern Web Technologies",
  description = "Experienced full-stack developer from Port Harcourt, Nigeria. Specializing in React, Node.js, TypeScript, and modern web technologies. Built healthcare, real estate, and fintech applications with proven business impact.",
  keywords = "full-stack developer, react developer, nodejs, typescript, portfolio, web development, healthcare tech, real estate tech, fintech, port harcourt developer, nigeria developer, javascript, python, aws",
  image = "https://rayner.dev/og-image.png",
  url = "https://rayner.dev",
  type = "website",
  author = "Rayner",
  publishedTime,
  modifiedTime,
  structuredData
}: SEOHeadProps) => {
  
  useEffect(() => {
    // Update document title
    document.title = title;

    // Helper function to update or create meta tags
    const updateMetaTag = (property: string, content: string, isProperty = false) => {
      const selector = isProperty ? `meta[property="${property}"]` : `meta[name="${property}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (isProperty) {
          meta.setAttribute('property', property);
        } else {
          meta.setAttribute('name', property);
        }
        document.head.appendChild(meta);
      }
      
      meta.setAttribute('content', content);
    };

    // Update basic meta tags
    updateMetaTag('description', description);
    updateMetaTag('keywords', keywords);
    updateMetaTag('author', author);

    // Update Open Graph tags
    updateMetaTag('og:title', title, true);
    updateMetaTag('og:description', description, true);
    updateMetaTag('og:image', image, true);
    updateMetaTag('og:url', url, true);
    updateMetaTag('og:type', type, true);

    // Update Twitter tags
    updateMetaTag('twitter:title', title, true);
    updateMetaTag('twitter:description', description, true);
    updateMetaTag('twitter:image', image, true);
    updateMetaTag('twitter:url', url, true);

    // Update article tags if provided
    if (publishedTime) {
      updateMetaTag('article:published_time', publishedTime, true);
    }
    if (modifiedTime) {
      updateMetaTag('article:modified_time', modifiedTime, true);
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
    if (!canonical) {
      canonical = document.createElement('link');
      canonical.setAttribute('rel', 'canonical');
      document.head.appendChild(canonical);
    }
    canonical.setAttribute('href', url);

    // Add structured data if provided
    if (structuredData) {
      let script = document.querySelector('script[type="application/ld+json"]#dynamic-structured-data') as HTMLScriptElement;
      if (!script) {
        script = document.createElement('script');
        script.setAttribute('type', 'application/ld+json');
        script.setAttribute('id', 'dynamic-structured-data');
        document.head.appendChild(script);
      }
      script.textContent = JSON.stringify(structuredData);
    }

  }, [title, description, keywords, image, url, type, author, publishedTime, modifiedTime, structuredData]);

  return null; // This component doesn't render anything
};

export default SEOHead;

// Predefined SEO configurations for different pages
export const seoConfigs = {
  home: {
    title: "Rayner - Full Stack Developer | React, Node.js & Modern Web Technologies",
    description: "Experienced full-stack developer from Port Harcourt, Nigeria. Specializing in React, Node.js, TypeScript, and modern web technologies. Built healthcare, real estate, and fintech applications with proven business impact.",
    url: "https://rayner.dev",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "Person",
      "name": "Rayner",
      "jobTitle": "Full Stack Developer",
      "description": "Experienced full-stack developer specializing in React, Node.js, TypeScript, and modern web technologies.",
      "url": "https://rayner.dev"
    }
  },
  
  project: (projectTitle: string, projectDescription: string, projectId: string) => ({
    title: `${projectTitle} - Project by Rayner | Full Stack Developer`,
    description: projectDescription,
    url: `https://rayner.dev/project/${projectId}`,
    type: "article",
    structuredData: {
      "@context": "https://schema.org",
      "@type": "CreativeWork",
      "name": projectTitle,
      "description": projectDescription,
      "author": {
        "@type": "Person",
        "name": "Rayner"
      },
      "url": `https://rayner.dev/project/${projectId}`
    }
  }),

  notFound: {
    title: "Page Not Found - Rayner Portfolio",
    description: "The page you're looking for doesn't exist. Explore Rayner's portfolio of full-stack development projects.",
    url: "https://rayner.dev/404"
  }
};
