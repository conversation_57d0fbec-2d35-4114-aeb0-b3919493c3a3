import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useKonamiCode, useKeySequence } from '@/hooks/use-konami-code';
import { useToast } from '@/hooks/use-toast';
import { useAnalytics } from '@/hooks/use-analytics';

const EasterEggs = () => {
  const [konamiTriggered, setKonamiTriggered] = useState(false);
  const [matrixMode, setMatrixMode] = useState(false);
  const [rainbowMode, setRainbowMode] = useState(false);
  const [secretMessage, setSecretMessage] = useState('');
  const { toast } = useToast();
  const { trackEasterEggFound } = useAnalytics();

  // Konami Code Easter Egg
  useKonamiCode(() => {
    setKonamiTriggered(true);
    trackEasterEggFound('konami_code');
    toast({
      title: "🎉 Konami Code Activated!",
      description: "You've unlocked the secret developer mode! 🚀",
    });

    // Add some fun effects
    document.body.style.animation = 'rainbow 2s infinite';
    setTimeout(() => {
      document.body.style.animation = '';
      setKonamiTriggered(false);
    }, 5000);
  });

  // Matrix Mode (type "MATRIX")
  useKeySequence(['KeyM', 'KeyA', 'KeyT', 'KeyR', 'KeyI', 'KeyX'], () => {
    setMatrixMode(!matrixMode);
    trackEasterEggFound('matrix_mode');
    toast({
      title: matrixMode ? "🔴 Exiting Matrix..." : "💊 Welcome to the Matrix",
      description: matrixMode ? "Back to reality!" : "Follow the white rabbit...",
    });
  });

  // Rainbow Mode (type "RAINBOW")
  useKeySequence(['KeyR', 'KeyA', 'KeyI', 'KeyN', 'KeyB', 'KeyO', 'KeyW'], () => {
    setRainbowMode(!rainbowMode);
    toast({
      title: rainbowMode ? "🌈 Rainbow Mode OFF" : "🌈 Rainbow Mode ON",
      description: rainbowMode ? "Colors normalized" : "Taste the rainbow!",
    });
  });

  // Secret Message (type "SECRET")
  useKeySequence(['KeyS', 'KeyE', 'KeyC', 'KeyR', 'KeyE', 'KeyT'], () => {
    const messages = [
      "🤫 You found a secret! I love building hidden features.",
      "🎯 Fun fact: This portfolio has 7 hidden easter eggs!",
      "💡 Try clicking the logo 10 times...",
      "🚀 I'm always open to exciting opportunities!",
      "🎮 Gaming reference: Up, Up, Down, Down, Left, Right, Left, Right, B, A",
    ];
    const randomMessage = messages[Math.floor(Math.random() * messages.length)];
    setSecretMessage(randomMessage);
    
    toast({
      title: "🔍 Secret Discovered!",
      description: randomMessage,
    });

    setTimeout(() => setSecretMessage(''), 5000);
  });

  // Matrix Rain Effect
  useEffect(() => {
    if (!matrixMode) return;

    const canvas = document.createElement('canvas');
    canvas.style.position = 'fixed';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.pointerEvents = 'none';
    canvas.style.zIndex = '9999';
    canvas.style.opacity = '0.1';
    document.body.appendChild(canvas);

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    canvas.width = window.innerWidth;
    canvas.height = window.innerHeight;

    const matrix = "ABCDEFGHIJKLMNOPQRSTUVWXYZ123456789@#$%^&*()*&^%+-/~{[|`]}";
    const matrixArray = matrix.split("");
    const fontSize = 10;
    const columns = canvas.width / fontSize;
    const drops: number[] = [];

    for (let x = 0; x < columns; x++) {
      drops[x] = 1;
    }

    const draw = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.04)';
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      
      ctx.fillStyle = '#0F0';
      ctx.font = fontSize + 'px arial';
      
      for (let i = 0; i < drops.length; i++) {
        const text = matrixArray[Math.floor(Math.random() * matrixArray.length)];
        ctx.fillText(text, i * fontSize, drops[i] * fontSize);
        
        if (drops[i] * fontSize > canvas.height && Math.random() > 0.975) {
          drops[i] = 0;
        }
        drops[i]++;
      }
    };

    const interval = setInterval(draw, 35);

    return () => {
      clearInterval(interval);
      document.body.removeChild(canvas);
    };
  }, [matrixMode]);

  // Rainbow CSS injection
  useEffect(() => {
    if (rainbowMode) {
      const style = document.createElement('style');
      style.textContent = `
        @keyframes rainbow {
          0% { filter: hue-rotate(0deg); }
          100% { filter: hue-rotate(360deg); }
        }
        * {
          animation: rainbow 3s linear infinite !important;
        }
      `;
      document.head.appendChild(style);

      return () => {
        document.head.removeChild(style);
      };
    }
  }, [rainbowMode]);

  return (
    <>
      {/* Konami Code Celebration */}
      <AnimatePresence>
        {konamiTriggered && (
          <motion.div
            className="fixed inset-0 pointer-events-none z-50 flex items-center justify-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <motion.div
              className="text-6xl"
              animate={{
                scale: [1, 1.5, 1],
                rotate: [0, 360, 0],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
              }}
            >
              🎉🚀💻🎮✨
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Secret Message Display */}
      <AnimatePresence>
        {secretMessage && (
          <motion.div
            className="fixed bottom-4 right-4 bg-primary text-primary-foreground p-4 rounded-lg shadow-lg z-50 max-w-sm"
            initial={{ opacity: 0, y: 50, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 50, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <p className="text-sm">{secretMessage}</p>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Matrix Mode Indicator */}
      <AnimatePresence>
        {matrixMode && (
          <motion.div
            className="fixed top-4 left-4 bg-green-500 text-white px-3 py-1 rounded-full text-sm z-50"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            🔴 MATRIX MODE
          </motion.div>
        )}
      </AnimatePresence>

      {/* Rainbow Mode Indicator */}
      <AnimatePresence>
        {rainbowMode && (
          <motion.div
            className="fixed top-4 right-4 bg-gradient-to-r from-red-500 via-yellow-500 to-blue-500 text-white px-3 py-1 rounded-full text-sm z-50"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
          >
            🌈 RAINBOW MODE
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default EasterEggs;
