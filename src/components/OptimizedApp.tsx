import { Suspense, lazy, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useReducedMotion } from '@/hooks/use-reduced-motion';
import { SkeletonCard } from '@/components/LazyComponent';
import { onNetworkChange, getNetworkStatus } from '@/lib/sw-registration';

// Lazy load components for better performance
const EasterEggs = lazy(() => import('@/components/EasterEggs'));

interface OptimizedAppProps {
  children: React.ReactNode;
}

// Network status indicator
const NetworkStatus = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [showStatus, setShowStatus] = useState(false);

  useEffect(() => {
    const cleanup = onNetworkChange((online) => {
      setIsOnline(online);
      setShowStatus(true);
      
      // Hide status after 3 seconds
      setTimeout(() => setShowStatus(false), 3000);
    });

    return cleanup;
  }, []);

  if (!showStatus) return null;

  return (
    <AnimatePresence>
      <motion.div
        className={`fixed top-4 right-4 z-50 px-4 py-2 rounded-lg text-white text-sm font-medium ${
          isOnline ? 'bg-green-500' : 'bg-red-500'
        }`}
        initial={{ opacity: 0, y: -50, scale: 0.8 }}
        animate={{ opacity: 1, y: 0, scale: 1 }}
        exit={{ opacity: 0, y: -50, scale: 0.8 }}
        transition={{ type: 'spring', stiffness: 300 }}
      >
        {isOnline ? '🟢 Back online' : '🔴 No internet connection'}
      </motion.div>
    </AnimatePresence>
  );
};

// Performance warning for slow devices
const PerformanceWarning = () => {
  const [showWarning, setShowWarning] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  useEffect(() => {
    // Check device performance indicators
    const checkPerformance = () => {
      const connection = (navigator as any).connection;
      const memory = (performance as any).memory;
      
      let isSlowDevice = false;
      
      // Check connection speed
      if (connection && connection.effectiveType === 'slow-2g') {
        isSlowDevice = true;
      }
      
      // Check available memory (if supported)
      if (memory && memory.jsHeapSizeLimit < 100 * 1024 * 1024) { // Less than 100MB
        isSlowDevice = true;
      }
      
      // Check hardware concurrency
      if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 2) {
        isSlowDevice = true;
      }
      
      if (isSlowDevice && !prefersReducedMotion) {
        setShowWarning(true);
      }
    };

    // Check after a delay to avoid blocking initial render
    setTimeout(checkPerformance, 2000);
  }, [prefersReducedMotion]);

  if (!showWarning) return null;

  return (
    <motion.div
      className="fixed bottom-4 left-4 right-4 bg-yellow-500 text-yellow-900 p-4 rounded-lg shadow-lg z-50 max-w-md mx-auto"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ type: 'spring', stiffness: 300 }}
    >
      <div className="flex items-start space-x-3">
        <div className="text-xl">⚡</div>
        <div className="flex-1">
          <h4 className="font-semibold mb-1">Performance Mode Available</h4>
          <p className="text-sm mb-3">
            We've detected you might be on a slower device. Would you like to enable performance mode?
          </p>
          <div className="flex space-x-2">
            <button
              onClick={() => {
                // Enable performance mode
                document.body.classList.add('performance-mode');
                localStorage.setItem('performance-mode', 'true');
                setShowWarning(false);
              }}
              className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 transition-colors"
            >
              Enable
            </button>
            <button
              onClick={() => setShowWarning(false)}
              className="px-3 py-1 bg-yellow-200 text-yellow-800 rounded text-sm hover:bg-yellow-300 transition-colors"
            >
              Dismiss
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

// Resource hints component
const ResourceHints = () => {
  useEffect(() => {
    // Preload critical resources
    const criticalResources = [
      '/lovable-uploads/4ba070e6-32f0-4f98-a70c-8a6c72921958.png', // Hero image
    ];

    criticalResources.forEach((resource) => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = resource;
      document.head.appendChild(link);
    });

    // Prefetch likely next pages
    const prefetchResources = [
      '/project/phcityrent',
      '/project/medequip',
    ];

    // Prefetch after initial load
    setTimeout(() => {
      prefetchResources.forEach((resource) => {
        const link = document.createElement('link');
        link.rel = 'prefetch';
        link.href = resource;
        document.head.appendChild(link);
      });
    }, 3000);

    // DNS prefetch for external resources
    const dnsPrefetch = [
      'https://fonts.googleapis.com',
      'https://fonts.gstatic.com',
    ];

    dnsPrefetch.forEach((domain) => {
      const link = document.createElement('link');
      link.rel = 'dns-prefetch';
      link.href = domain;
      document.head.appendChild(link);
    });
  }, []);

  return null;
};

// Main optimized app wrapper
const OptimizedApp = ({ children }: OptimizedAppProps) => {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    // Mark as hydrated after initial render
    setIsHydrated(true);

    // Add performance mode class if previously enabled
    if (localStorage.getItem('performance-mode') === 'true') {
      document.body.classList.add('performance-mode');
    }

    // Add connection class for CSS optimizations
    const connection = (navigator as any).connection;
    if (connection) {
      document.body.classList.add(`connection-${connection.effectiveType}`);
    }

    // Add reduced motion class
    if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      document.body.classList.add('reduced-motion');
    }
  }, []);

  return (
    <>
      <ResourceHints />
      
      {/* Main app content */}
      <div className="min-h-screen">
        {children}
      </div>

      {/* Performance and network indicators */}
      {isHydrated && (
        <>
          <NetworkStatus />
          <PerformanceWarning />
          
          {/* Lazy load easter eggs */}
          <Suspense fallback={null}>
            <EasterEggs />
          </Suspense>
        </>
      )}
    </>
  );
};

export default OptimizedApp;

// Performance mode CSS (to be added to global styles)
export const performanceModeStyles = `
  .performance-mode * {
    animation-duration: 0.1s !important;
    animation-delay: 0s !important;
    transition-duration: 0.1s !important;
    transition-delay: 0s !important;
  }
  
  .performance-mode .animate-pulse,
  .performance-mode .animate-spin,
  .performance-mode .animate-bounce {
    animation: none !important;
  }
  
  .performance-mode [data-motion="true"] {
    transform: none !important;
  }
  
  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  /* Connection-based optimizations */
  .connection-slow-2g img:not([loading="eager"]) {
    loading: lazy;
  }
  
  .connection-slow-2g video {
    preload: none;
  }
`;
