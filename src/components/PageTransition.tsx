import { motion } from "framer-motion";
import { ReactNode } from "react";
import { useReducedMotion } from "@/hooks/use-reduced-motion";

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
}

const pageVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  in: {
    opacity: 1,
    y: 0,
    scale: 1
  },
  out: {
    opacity: 0,
    y: -20,
    scale: 1.02
  }
};

const pageTransition = {
  type: "tween",
  ease: "anticipate",
  duration: 0.4
};

const PageTransition = ({ children, className = "" }: PageTransitionProps) => {
  const prefersReducedMotion = useReducedMotion();

  const optimizedVariants = prefersReducedMotion
    ? {
        initial: { opacity: 0 },
        in: { opacity: 1 },
        out: { opacity: 0 }
      }
    : pageVariants;

  const optimizedTransition = prefersReducedMotion
    ? { duration: 0.1 }
    : pageTransition;

  return (
    <motion.div
      initial="initial"
      animate="in"
      exit="out"
      variants={optimizedVariants}
      transition={optimizedTransition}
      className={className}
      style={{ willChange: 'transform, opacity' }}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;
