import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface EnterpriseCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "elevated" | "premium" | "executive";
  size?: "sm" | "md" | "lg" | "xl";
  gradient?: boolean;
  glassmorphism?: boolean;
}

const EnterpriseCard = React.forwardRef<HTMLDivElement, EnterpriseCardProps>(
  ({ className, variant = "default", size = "md", gradient = false, glassmorphism = false, children, ...props }, ref) => {
    const variants = {
      default: "bg-card border border-border/50 shadow-sm",
      elevated: "bg-card border border-border/30 shadow-lg shadow-black/5",
      premium: "bg-gradient-to-br from-card via-card to-muted/20 border border-border/20 shadow-xl shadow-black/10",
      executive: "bg-gradient-to-br from-background via-card to-primary/5 border border-primary/20 shadow-2xl shadow-primary/10"
    };

    const sizes = {
      sm: "p-4",
      md: "p-6",
      lg: "p-8",
      xl: "p-10"
    };

    const baseClasses = cn(
      "rounded-xl transition-all duration-300 hover:shadow-lg hover:shadow-black/10",
      variants[variant],
      sizes[size],
      glassmorphism && "backdrop-blur-xl bg-opacity-80",
      gradient && "bg-gradient-to-br from-background via-card to-muted/10",
      className
    );

    return (
      <motion.div
        ref={ref}
        className={baseClasses}
        whileHover={{ 
          y: -4,
          transition: { type: "spring", stiffness: 300, damping: 30 }
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        {...props}
      >
        {children}
      </motion.div>
    );
  }
);

EnterpriseCard.displayName = "EnterpriseCard";

const EnterpriseCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-2 pb-6", className)}
    {...props}
  />
));
EnterpriseCardHeader.displayName = "EnterpriseCardHeader";

const EnterpriseCardTitle = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLHeadingElement>
>(({ className, ...props }, ref) => (
  <h3
    ref={ref}
    className={cn(
      "text-2xl font-bold leading-none tracking-tight bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent",
      className
    )}
    {...props}
  />
));
EnterpriseCardTitle.displayName = "EnterpriseCardTitle";

const EnterpriseCardDescription = React.forwardRef<
  HTMLParagraphElement,
  React.HTMLAttributes<HTMLParagraphElement>
>(({ className, ...props }, ref) => (
  <p
    ref={ref}
    className={cn("text-muted-foreground leading-relaxed", className)}
    {...props}
  />
));
EnterpriseCardDescription.displayName = "EnterpriseCardDescription";

const EnterpriseCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("space-y-4", className)} {...props} />
));
EnterpriseCardContent.displayName = "EnterpriseCardContent";

const EnterpriseCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center pt-6 border-t border-border/30", className)}
    {...props}
  />
));
EnterpriseCardFooter.displayName = "EnterpriseCardFooter";

export {
  EnterpriseCard,
  EnterpriseCardHeader,
  EnterpriseCardTitle,
  EnterpriseCardDescription,
  EnterpriseCardContent,
  EnterpriseCardFooter,
};

// Enterprise Badge Component
interface EnterpriseBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "success" | "warning" | "premium" | "executive";
  size?: "sm" | "md" | "lg";
}

export const EnterpriseBadge = React.forwardRef<HTMLDivElement, EnterpriseBadgeProps>(
  ({ className, variant = "default", size = "md", children, ...props }, ref) => {
    const variants = {
      default: "bg-muted text-muted-foreground border-border/50",
      success: "bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800",
      warning: "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800",
      premium: "bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-700 border-blue-200 dark:from-blue-950 dark:to-indigo-950 dark:text-blue-300 dark:border-blue-800",
      executive: "bg-gradient-to-r from-purple-50 to-pink-50 text-purple-700 border-purple-200 dark:from-purple-950 dark:to-pink-950 dark:text-purple-300 dark:border-purple-800"
    };

    const sizes = {
      sm: "px-2 py-1 text-xs",
      md: "px-3 py-1.5 text-sm",
      lg: "px-4 py-2 text-base"
    };

    return (
      <div
        ref={ref}
        className={cn(
          "inline-flex items-center rounded-full border font-medium transition-all duration-200",
          variants[variant],
          sizes[size],
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

EnterpriseBadge.displayName = "EnterpriseBadge";
