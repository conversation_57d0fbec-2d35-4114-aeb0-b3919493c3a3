// Image optimization utilities

interface ImageOptimizationOptions {
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill';
}

interface ResponsiveImageSizes {
  mobile: number;
  tablet: number;
  desktop: number;
  xl: number;
}

// Generate responsive image URLs (for future CDN integration)
export const generateResponsiveImageUrls = (
  baseUrl: string,
  sizes: ResponsiveImageSizes,
  options: ImageOptimizationOptions = {}
) => {
  const { quality = 80, format = 'webp' } = options;
  
  return {
    mobile: `${baseUrl}?w=${sizes.mobile}&q=${quality}&f=${format}`,
    tablet: `${baseUrl}?w=${sizes.tablet}&q=${quality}&f=${format}`,
    desktop: `${baseUrl}?w=${sizes.desktop}&q=${quality}&f=${format}`,
    xl: `${baseUrl}?w=${sizes.xl}&q=${quality}&f=${format}`,
  };
};

// Generate srcSet for responsive images
export const generateSrcSet = (baseUrl: string, sizes: number[], options: ImageOptimizationOptions = {}) => {
  const { quality = 80, format = 'webp' } = options;
  
  return sizes
    .map(size => `${baseUrl}?w=${size}&q=${quality}&f=${format} ${size}w`)
    .join(', ');
};

// Optimize image on client side (for user uploads)
export const optimizeImage = (
  file: File,
  options: ImageOptimizationOptions = {}
): Promise<Blob> => {
  return new Promise((resolve, reject) => {
    const {
      quality = 0.8,
      format = 'webp',
      width,
      height,
      fit = 'cover'
    } = options;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      // Calculate dimensions
      let { width: imgWidth, height: imgHeight } = img;
      let targetWidth = width || imgWidth;
      let targetHeight = height || imgHeight;

      if (width && height) {
        if (fit === 'cover') {
          const ratio = Math.max(width / imgWidth, height / imgHeight);
          targetWidth = imgWidth * ratio;
          targetHeight = imgHeight * ratio;
        } else if (fit === 'contain') {
          const ratio = Math.min(width / imgWidth, height / imgHeight);
          targetWidth = imgWidth * ratio;
          targetHeight = imgHeight * ratio;
        }
      } else if (width) {
        targetHeight = (imgHeight * width) / imgWidth;
        targetWidth = width;
      } else if (height) {
        targetWidth = (imgWidth * height) / imgHeight;
        targetHeight = height;
      }

      canvas.width = targetWidth;
      canvas.height = targetHeight;

      // Draw and optimize
      ctx.drawImage(img, 0, 0, targetWidth, targetHeight);

      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to optimize image'));
          }
        },
        `image/${format}`,
        quality
      );
    };

    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = URL.createObjectURL(file);
  });
};

// Check if WebP is supported
export const isWebPSupported = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const webP = new Image();
    webP.onload = webP.onerror = () => {
      resolve(webP.height === 2);
    };
    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
  });
};

// Check if AVIF is supported
export const isAVIFSupported = (): Promise<boolean> => {
  return new Promise((resolve) => {
    const avif = new Image();
    avif.onload = avif.onerror = () => {
      resolve(avif.height === 2);
    };
    avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
  });
};

// Get optimal image format based on browser support
export const getOptimalImageFormat = async (): Promise<'avif' | 'webp' | 'jpeg'> => {
  if (await isAVIFSupported()) {
    return 'avif';
  } else if (await isWebPSupported()) {
    return 'webp';
  } else {
    return 'jpeg';
  }
};

// Preload critical images
export const preloadImage = (src: string, crossOrigin?: string): Promise<void> => {
  return new Promise((resolve, reject) => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    if (crossOrigin) {
      link.crossOrigin = crossOrigin;
    }

    link.onload = () => resolve();
    link.onerror = () => reject(new Error(`Failed to preload image: ${src}`));

    document.head.appendChild(link);
  });
};

// Lazy load images with Intersection Observer
export const createImageObserver = (
  callback: (entry: IntersectionObserverEntry) => void,
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const defaultOptions: IntersectionObserverInit = {
    rootMargin: '50px 0px',
    threshold: 0.01,
    ...options
  };

  return new IntersectionObserver((entries) => {
    entries.forEach(callback);
  }, defaultOptions);
};

// Image loading performance metrics
export const measureImageLoadTime = (src: string): Promise<number> => {
  return new Promise((resolve, reject) => {
    const startTime = performance.now();
    const img = new Image();

    img.onload = () => {
      const loadTime = performance.now() - startTime;
      resolve(loadTime);
    };

    img.onerror = () => {
      reject(new Error(`Failed to load image: ${src}`));
    };

    img.src = src;
  });
};

// Generate blur placeholder from image
export const generateBlurPlaceholder = (
  img: HTMLImageElement,
  width = 10,
  height = 10
): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');

  if (!ctx) return '';

  canvas.width = width;
  canvas.height = height;

  ctx.filter = 'blur(2px)';
  ctx.drawImage(img, 0, 0, width, height);

  return canvas.toDataURL('image/jpeg', 0.1);
};

// Common responsive image sizes
export const RESPONSIVE_SIZES: ResponsiveImageSizes = {
  mobile: 480,
  tablet: 768,
  desktop: 1024,
  xl: 1440
};

// Image optimization presets
export const IMAGE_PRESETS = {
  thumbnail: { width: 150, height: 150, quality: 70, format: 'webp' as const },
  card: { width: 400, height: 300, quality: 80, format: 'webp' as const },
  hero: { width: 1200, height: 600, quality: 85, format: 'webp' as const },
  avatar: { width: 200, height: 200, quality: 80, format: 'webp' as const },
  gallery: { width: 800, height: 600, quality: 85, format: 'webp' as const }
};

// Utility to get image dimensions
export const getImageDimensions = (src: string): Promise<{ width: number; height: number }> => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => {
      resolve({ width: img.naturalWidth, height: img.naturalHeight });
    };
    img.onerror = () => reject(new Error('Failed to load image'));
    img.src = src;
  });
};
