import { Variants, Transition } from 'framer-motion';

/**
 * Optimized animation variants that respect user preferences
 */
export const createOptimizedVariants = (
  normalVariants: Variants,
  reducedVariants?: Variants
): Variants => {
  const prefersReducedMotion = 
    typeof window !== 'undefined' && 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  if (prefersReducedMotion) {
    return reducedVariants || {
      initial: { opacity: 0 },
      animate: { opacity: 1 },
      exit: { opacity: 0 }
    };
  }

  return normalVariants;
};

/**
 * Optimized transition that respects user preferences
 */
export const createOptimizedTransition = (
  normalTransition: Transition,
  reducedTransition?: Transition
): Transition => {
  const prefersReducedMotion = 
    typeof window !== 'undefined' && 
    window.matchMedia('(prefers-reduced-motion: reduce)').matches;

  if (prefersReducedMotion) {
    return reducedTransition || { duration: 0.1 };
  }

  return normalTransition;
};

/**
 * Common animation variants for consistent use across components
 */
export const commonVariants = {
  // Fade animations
  fadeIn: createOptimizedVariants({
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  }),

  // Slide animations
  slideUp: createOptimizedVariants({
    initial: { opacity: 0, y: 50 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -50 }
  }),

  slideDown: createOptimizedVariants({
    initial: { opacity: 0, y: -50 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 50 }
  }),

  slideLeft: createOptimizedVariants({
    initial: { opacity: 0, x: 50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -50 }
  }),

  slideRight: createOptimizedVariants({
    initial: { opacity: 0, x: -50 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 50 }
  }),

  // Scale animations
  scaleIn: createOptimizedVariants({
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 }
  }),

  // Stagger container
  staggerContainer: createOptimizedVariants({
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.1
      }
    }
  }),

  // Stagger item
  staggerItem: createOptimizedVariants({
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 }
  })
};

/**
 * Common transitions for consistent timing
 */
export const commonTransitions = {
  smooth: createOptimizedTransition({
    type: "tween",
    ease: "easeOut",
    duration: 0.3
  }),

  spring: createOptimizedTransition({
    type: "spring",
    stiffness: 300,
    damping: 30
  }),

  bouncy: createOptimizedTransition({
    type: "spring",
    stiffness: 400,
    damping: 10
  }),

  slow: createOptimizedTransition({
    type: "tween",
    ease: "easeOut",
    duration: 0.6
  })
};

/**
 * Performance optimization: Use transform properties for better performance
 */
export const performantProps = {
  // Use transform instead of changing layout properties
  layout: false,
  // Enable hardware acceleration
  style: {
    willChange: 'transform, opacity'
  }
};
