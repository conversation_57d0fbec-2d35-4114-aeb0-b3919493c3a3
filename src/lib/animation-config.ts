/**
 * Global animation configuration
 * Centralizes animation settings for consistency and performance
 */

// Check if user prefers reduced motion
export const prefersReducedMotion = () => {
  if (typeof window === 'undefined') return false;
  return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
};

// Animation durations
export const ANIMATION_DURATIONS = {
  fast: prefersReducedMotion() ? 0.1 : 0.2,
  normal: prefersReducedMotion() ? 0.1 : 0.4,
  slow: prefersReducedMotion() ? 0.1 : 0.6,
  verySlow: prefersReducedMotion() ? 0.1 : 1.0,
};

// Easing functions
export const EASING = {
  easeOut: [0.0, 0.0, 0.2, 1],
  easeIn: [0.4, 0.0, 1, 1],
  easeInOut: [0.4, 0.0, 0.2, 1],
  bounce: [0.68, -0.55, 0.265, 1.55],
};

// Spring configurations
export const SPRING_CONFIGS = {
  gentle: { stiffness: 120, damping: 14 },
  wobbly: { stiffness: 180, damping: 12 },
  stiff: { stiffness: 300, damping: 30 },
  slow: { stiffness: 280, damping: 60 },
};

// Common animation variants
export const VARIANTS = {
  fadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
  },
  
  slideDown: {
    initial: { opacity: 0, y: -20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: 20 },
  },
  
  slideLeft: {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
  },
  
  slideRight: {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 20 },
  },
  
  scaleIn: {
    initial: { opacity: 0, scale: 0.9 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.9 },
  },
  
  staggerContainer: {
    animate: {
      transition: {
        staggerChildren: prefersReducedMotion() ? 0 : 0.1,
      },
    },
  },
  
  staggerItem: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
  },
};

// Performance optimizations
export const PERFORMANCE_CONFIG = {
  // Use transform instead of layout properties
  willChange: 'transform, opacity',
  
  // Reduce motion for accessibility
  reducedMotion: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.1 },
  },
  
  // GPU acceleration
  style: {
    transform: 'translateZ(0)',
    backfaceVisibility: 'hidden' as const,
    perspective: 1000,
  },
};

// GSAP ScrollTrigger defaults
export const SCROLL_TRIGGER_DEFAULTS = {
  start: 'top 80%',
  end: 'bottom 20%',
  toggleActions: 'play none none reverse' as const,
  // Disable for reduced motion
  disabled: prefersReducedMotion(),
};

// Intersection Observer options for scroll animations
export const INTERSECTION_OPTIONS = {
  threshold: 0.1,
  rootMargin: '0px 0px -10% 0px',
};
