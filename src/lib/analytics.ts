// Plausible Analytics Integration
declare global {
  interface Window {
    plausible?: (event: string, options?: { props?: Record<string, string | number> }) => void;
  }
}

interface AnalyticsEvent {
  name: string;
  props?: Record<string, string | number>;
}

class Analytics {
  private isEnabled: boolean = false;
  private domain: string = '';

  constructor() {
    // Only enable analytics in production
    this.isEnabled = process.env.NODE_ENV === 'production';
    this.domain = window.location.hostname;
  }

  // Initialize Plausible Analytics
  init(domain?: string) {
    if (!this.isEnabled) {
      console.log('Analytics disabled in development mode');
      return;
    }

    if (domain) {
      this.domain = domain;
    }

    // Load Plausible script
    const script = document.createElement('script');
    script.defer = true;
    script.src = 'https://plausible.io/js/script.js';
    script.setAttribute('data-domain', this.domain);
    document.head.appendChild(script);

    console.log('Plausible Analytics initialized for domain:', this.domain);
  }

  // Track custom events
  track(event: string, props?: Record<string, string | number>) {
    if (!this.isEnabled || !window.plausible) {
      console.log('Analytics event (dev):', event, props);
      return;
    }

    try {
      window.plausible(event, { props });
      console.log('Analytics event tracked:', event, props);
    } catch (error) {
      console.error('Analytics tracking error:', error);
    }
  }

  // Predefined event tracking methods
  trackPageView(page: string) {
    this.track('pageview', { page });
  }

  trackProjectView(projectId: string, projectTitle: string) {
    this.track('project_view', { 
      project_id: projectId,
      project_title: projectTitle 
    });
  }

  trackContactFormSubmit() {
    this.track('contact_form_submit');
  }

  trackResumeDownload() {
    this.track('resume_download');
  }

  trackEasterEggFound(easterEgg: string) {
    this.track('easter_egg_found', { easter_egg: easterEgg });
  }

  trackSocialClick(platform: string) {
    this.track('social_click', { platform });
  }

  trackSkillInteraction(skill: string) {
    this.track('skill_interaction', { skill });
  }

  trackThemeToggle(theme: string) {
    this.track('theme_toggle', { theme });
  }

  trackScrollDepth(depth: number) {
    this.track('scroll_depth', { depth_percentage: depth });
  }

  trackTimeOnPage(seconds: number) {
    this.track('time_on_page', { seconds });
  }

  // Track external link clicks
  trackExternalLink(url: string, context: string) {
    this.track('external_link_click', { 
      url,
      context 
    });
  }

  // Track search interactions (if you add search functionality)
  trackSearch(query: string, results: number) {
    this.track('search', { 
      query,
      results_count: results 
    });
  }

  // Track performance metrics
  trackPerformance() {
    if (!this.isEnabled || typeof window.performance === 'undefined') return;

    // Track page load time
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        if (navigation) {
          const loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart);
          this.track('page_load_time', { load_time_ms: loadTime });
        }
      }, 0);
    });
  }

  // Track user engagement
  trackEngagement() {
    if (!this.isEnabled) return;

    let startTime = Date.now();
    let maxScroll = 0;

    // Track scroll depth
    const trackScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      );
      
      if (scrollPercent > maxScroll) {
        maxScroll = scrollPercent;
        
        // Track milestone scroll depths
        if ([25, 50, 75, 90].includes(scrollPercent)) {
          this.trackScrollDepth(scrollPercent);
        }
      }
    };

    // Track time on page when user leaves
    const trackTimeOnPage = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000);
      if (timeSpent > 10) { // Only track if user spent more than 10 seconds
        this.trackTimeOnPage(timeSpent);
      }
    };

    window.addEventListener('scroll', trackScroll, { passive: true });
    window.addEventListener('beforeunload', trackTimeOnPage);
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        trackTimeOnPage();
      }
    });
  }
}

// Create singleton instance
const analytics = new Analytics();

export default analytics;

// Export individual methods for convenience
export const {
  init: initAnalytics,
  track: trackEvent,
  trackPageView,
  trackProjectView,
  trackContactFormSubmit,
  trackResumeDownload,
  trackEasterEggFound,
  trackSocialClick,
  trackSkillInteraction,
  trackThemeToggle,
  trackExternalLink,
  trackPerformance,
  trackEngagement
} = analytics;
