#!/usr/bin/env node

/**
 * Bundle Analysis Script
 * Analyzes the built bundle for performance insights
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

const DIST_DIR = path.join(__dirname, '../dist');
const ASSETS_DIR = path.join(DIST_DIR, 'assets');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function analyzeBundle() {
  log('\n🔍 Bundle Analysis Report', 'cyan');
  log('=' * 50, 'cyan');

  if (!fs.existsSync(DIST_DIR)) {
    log('❌ Build directory not found. Please run "npm run build" first.', 'red');
    process.exit(1);
  }

  // Analyze JavaScript files
  const jsFiles = fs.readdirSync(ASSETS_DIR)
    .filter(file => file.endsWith('.js'))
    .map(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        type: file.includes('vendor') ? 'vendor' : 'app'
      };
    })
    .sort((a, b) => b.size - a.size);

  // Analyze CSS files
  const cssFiles = fs.readdirSync(ASSETS_DIR)
    .filter(file => file.endsWith('.css'))
    .map(file => {
      const filePath = path.join(ASSETS_DIR, file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        type: 'styles'
      };
    })
    .sort((a, b) => b.size - a.size);

  // Calculate totals
  const totalJsSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCssSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const totalSize = totalJsSize + totalCssSize;

  // Display results
  log('\n📊 File Size Analysis:', 'bright');
  log('-'.repeat(60));

  log('\n🟨 JavaScript Files:', 'yellow');
  jsFiles.forEach(file => {
    const sizeStr = formatBytes(file.size);
    const percentage = ((file.size / totalSize) * 100).toFixed(1);
    const color = file.type === 'vendor' ? 'magenta' : 'blue';
    log(`  ${file.name.padEnd(40)} ${sizeStr.padStart(10)} (${percentage}%)`, color);
  });

  log('\n🟦 CSS Files:', 'blue');
  cssFiles.forEach(file => {
    const sizeStr = formatBytes(file.size);
    const percentage = ((file.size / totalSize) * 100).toFixed(1);
    log(`  ${file.name.padEnd(40)} ${sizeStr.padStart(10)} (${percentage}%)`, 'cyan');
  });

  log('\n📈 Summary:', 'bright');
  log(`  Total JavaScript: ${formatBytes(totalJsSize)}`, 'yellow');
  log(`  Total CSS: ${formatBytes(totalCssSize)}`, 'blue');
  log(`  Total Bundle Size: ${formatBytes(totalSize)}`, 'green');

  // Performance recommendations
  log('\n💡 Performance Recommendations:', 'bright');
  log('-'.repeat(60));

  if (totalJsSize > 500 * 1024) { // 500KB
    log('  ⚠️  JavaScript bundle is large (>500KB). Consider code splitting.', 'yellow');
  }

  if (totalCssSize > 100 * 1024) { // 100KB
    log('  ⚠️  CSS bundle is large (>100KB). Consider removing unused styles.', 'yellow');
  }

  const largeJsFiles = jsFiles.filter(file => file.size > 200 * 1024);
  if (largeJsFiles.length > 0) {
    log('  ⚠️  Large JavaScript files detected:', 'yellow');
    largeJsFiles.forEach(file => {
      log(`     - ${file.name} (${formatBytes(file.size)})`, 'yellow');
    });
  }

  if (totalSize < 300 * 1024) {
    log('  ✅ Bundle size is optimal (<300KB)', 'green');
  } else if (totalSize < 500 * 1024) {
    log('  ⚠️  Bundle size is acceptable but could be optimized', 'yellow');
  } else {
    log('  ❌ Bundle size is too large (>500KB)', 'red');
  }

  // Gzip analysis (if gzip files exist)
  log('\n🗜️  Compression Analysis:', 'bright');
  const gzipFiles = fs.readdirSync(ASSETS_DIR)
    .filter(file => file.endsWith('.gz'));

  if (gzipFiles.length > 0) {
    gzipFiles.forEach(file => {
      const originalFile = file.replace('.gz', '');
      const gzipPath = path.join(ASSETS_DIR, file);
      const originalPath = path.join(ASSETS_DIR, originalFile);
      
      if (fs.existsSync(originalPath)) {
        const originalSize = fs.statSync(originalPath).size;
        const gzipSize = fs.statSync(gzipPath).size;
        const compression = ((1 - gzipSize / originalSize) * 100).toFixed(1);
        
        log(`  ${originalFile.padEnd(40)} ${formatBytes(gzipSize).padStart(10)} (${compression}% compressed)`, 'green');
      }
    });
  } else {
    log('  No gzipped files found. Enable gzip compression for better performance.', 'yellow');
  }

  // Check for source maps in production
  const sourceMaps = fs.readdirSync(ASSETS_DIR)
    .filter(file => file.endsWith('.map'));

  if (sourceMaps.length > 0) {
    log('\n⚠️  Source maps found in production build:', 'yellow');
    sourceMaps.forEach(file => {
      log(`  - ${file}`, 'yellow');
    });
    log('  Consider removing source maps from production builds.', 'yellow');
  }

  log('\n✨ Analysis complete!', 'green');
}

// Check if we should run webpack-bundle-analyzer
function runBundleAnalyzer() {
  try {
    log('\n🚀 Running webpack-bundle-analyzer...', 'cyan');
    execSync('npx webpack-bundle-analyzer dist/stats.json', { stdio: 'inherit' });
  } catch (error) {
    log('❌ webpack-bundle-analyzer not available or stats.json not found', 'red');
    log('   Run "npm run build -- --analyze" to generate stats.json', 'yellow');
  }
}

// Main execution
function main() {
  const args = process.argv.slice(2);
  
  analyzeBundle();
  
  if (args.includes('--interactive') || args.includes('-i')) {
    runBundleAnalyzer();
  }
  
  if (args.includes('--help') || args.includes('-h')) {
    log('\nUsage:', 'bright');
    log('  node scripts/analyze-bundle.js [options]', 'cyan');
    log('\nOptions:', 'bright');
    log('  -i, --interactive    Open interactive bundle analyzer', 'cyan');
    log('  -h, --help          Show this help message', 'cyan');
  }
}

if (require.main === module) {
  main();
}

module.exports = { analyzeBundle, formatBytes };
